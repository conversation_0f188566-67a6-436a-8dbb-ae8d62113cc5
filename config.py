"""
浮标-无人机交互环境配置文件
"""

import numpy as np

# 系统规模配置
NUM_BUOYS = 200  # 浮标数量
NUM_UAVS = 6  # 无人机数量

# 空间配置
AREA_SIZE = 10000  # 10km x 10km (单位: 米)
MAX_COMM_DISTANCE = 3000  # 最大通信距离 (单位: 米)
UAV_HEIGHT = 0  # 无人机高度，2D模型，不考虑高度

# 时间配置
BUOY_COMM_UPDATE_PERIOD = 200  # 浮标通信更新周期 (单位: 秒)
UAV_DECISION_PERIOD = 10  # 无人机决策周期 (单位: 秒)
SIMULATION_PRECISION = 1  # 仿真时钟精度 (单位: 秒)

# 无人机配置
UAV_SPEED = 16.67  # 无人机速度 (单位: 米/秒)
UAV_STEP_DISTANCE = 166.7  # 无人机每步移动距离 (单位: 米)
UAV_INTERFERENCE_RADIUS = 1000  # 无人机干扰半径 (单位: 米)

# 通信和干扰配置
NUM_COMM_MODES = 20  # 通信方式数量 (固定为20种)
NUM_INTERFERENCE_MODES = 10  # 干扰模式数量 (固定为10种)

# 干扰效果表 (10种干扰模式对20种通信方式的影响系数)
INTERFERENCE_EFFECT = np.array([
    [0.85, 0.10, 0.30, 0.45, 0.60, 0.25, 0.70, 0.15, 0.50, 0.35, 0.20, 0.65, 0.40, 0.55, 0.75, 0.05, 0.80, 0.90, 0.25, 0.50],
    [0.20, 0.75, 0.85, 0.30, 0.45, 0.60, 0.15, 0.70, 0.25, 0.50, 0.35, 0.10, 0.65, 0.40, 0.55, 0.80, 0.05, 0.30, 0.90, 0.45],
    [0.50, 0.35, 0.20, 0.95, 0.10, 0.30, 0.45, 0.60, 0.25, 0.70, 0.15, 0.50, 0.35, 0.80, 0.05, 0.40, 0.55, 0.75, 0.65, 0.90],
    [0.25, 0.70, 0.15, 0.50, 0.85, 0.10, 0.30, 0.45, 0.60, 0.25, 0.70, 0.15, 0.50, 0.35, 0.20, 0.65, 0.40, 0.55, 0.75, 0.05],
    [0.60, 0.25, 0.70, 0.15, 0.50, 0.95, 0.10, 0.30, 0.45, 0.60, 0.25, 0.70, 0.15, 0.50, 0.35, 0.20, 0.65, 0.40, 0.55, 0.75],
    [0.35, 0.20, 0.65, 0.40, 0.55, 0.75, 0.05, 0.80, 0.90, 0.25, 0.50, 0.35, 0.20, 0.65, 0.40, 0.55, 0.75, 0.05, 0.80, 0.90],
    [0.70, 0.15, 0.50, 0.35, 0.20, 0.65, 0.80, 0.05, 0.30, 0.90, 0.45, 0.60, 0.25, 0.70, 0.15, 0.50, 0.35, 0.20, 0.65, 0.40],
    [0.10, 0.30, 0.45, 0.60, 0.25, 0.70, 0.15, 0.50, 0.35, 0.20, 0.65, 0.40, 0.55, 0.75, 0.05, 0.80, 0.90, 0.25, 0.50, 0.35],
    [0.45, 0.60, 0.25, 0.70, 0.15, 0.50, 0.35, 0.20, 0.65, 0.40, 0.55, 0.75, 0.05, 0.80, 0.90, 0.25, 0.50, 0.35, 0.20, 0.65],
    [0.15, 0.50, 0.35, 0.20, 0.65, 0.40, 0.55, 0.75, 0.05, 0.80, 0.90, 0.25, 0.50, 0.35, 0.20, 0.65, 0.40, 0.55, 0.75, 0.05]
])

# 渲染配置
WINDOW_SIZE = 800  # 渲染窗口大小
RENDER_FPS = 1  # 渲染帧率
VISUALIZATION_ENABLED = True  # 可视化开关

# 训练配置
LEARNING_RATE = 3e-4
N_STEPS = 2048
BATCH_SIZE = 64
N_EPOCHS = 10
GAMMA = 0.99
GAE_LAMBDA = 0.95
CLIP_RANGE = 0.2
ENT_COEF = 0.01
NUM_ENVS = 4
TOTAL_TIMESTEPS = 1000000
SAVE_FREQ = 10000

# 训练方案配置
TRAINING_APPROACH = "combined"  # 可选: "movement", "interference", "combined", "hierarchical"

# 奖励函数配置
REWARD_WEIGHTS = {
    "communication_quality": -1.0,  # 通信质量的权重（负值表示我们希望降低通信质量）
    "coverage": 0.5,  # 覆盖率的权重
    "interference_efficiency": 0.3,  # 干扰效率的权重
    "movement_penalty": -0.1  # 移动惩罚的权重
}

# 文件路径配置
LOG_DIR = "./logs"
MODEL_DIR = "./models"
TENSORBOARD_DIR = "./tensorboard_logs"
PLOT_DIR = "./plots" 
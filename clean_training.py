#!/usr/bin/env python3
"""
极简训练脚本
直接环境交互，保存训练曲线和模型

"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from environment.buoy_env import BuoyEnvironment


class SimpleTrainer:
    """简单训练器"""
    
    def __init__(self, env):
        self.env = env
        self.episode_rewards = []
        self.episode_lengths = []
        self.step_rewards = []
        
        # 简单的策略参数
        self.exploration_rate = 0.3  # 探索率
        self.learning_rate = 0.01    # 学习率（虽然这里没有真正的学习）
        
    def train(self, total_timesteps=50000):
        """训练过程"""
        print(f"开始训练 {total_timesteps} 步...")
        
        step = 0
        episode = 0
        
        while step < total_timesteps:
            obs, info = self.env.reset()
            episode_reward = 0
            episode_length = 0
            done = False
            truncated = False
            
            while not (done or truncated) and step < total_timesteps:
                # 选择动作
                action = self._select_action(obs, step, total_timesteps)
                
                # 执行动作
                obs, reward, done, truncated, info = self.env.step(action)
                
                # 记录数据
                episode_reward += reward
                episode_length += 1
                step += 1
                
                self.step_rewards.append(reward)
                
                # 打印进度
                if step % 5000 == 0:
                    print(f"Step {step}/{total_timesteps}, Episode {episode+1}")
            
            # 记录episode数据
            self.episode_rewards.append(episode_reward)
            self.episode_lengths.append(episode_length)
            episode += 1
            
            # 打印episode结果
            if episode % 10 == 0:
                recent_rewards = self.episode_rewards[-10:]
                avg_reward = np.mean(recent_rewards)
                print(f"Episode {episode}: Avg Reward (last 10) = {avg_reward:.4f}")
        
        print(f"训练完成！总共 {episode} 个episodes")
        return self
    
    def _select_action(self, obs, current_step, total_steps):
        """选择动作 - 改进的策略"""
        # 动态调整探索率（从高到低）
        progress = current_step / total_steps
        current_exploration = self.exploration_rate * (1 - progress * 0.8)
        
        if np.random.random() < current_exploration:
            # 随机探索
            return self.env.action_space.sample()
        else:
            # 使用启发式策略
            return self._heuristic_action(obs)
    
    def _heuristic_action(self, obs):
        """启发式动作选择"""
        action = self.env.action_space.sample()

        # 这里可以根据观察状态实现更智能的策略
        # 目前使用改进的随机策略

        # 检查训练方法和动作空间
        training_approach = getattr(self.env, 'training_approach', 'interference')

        if training_approach == 'interference':
            # 对于干扰模式，动作空间是 [干扰模式] * num_uavs
            num_uavs = NUM_UAVS
            for i in range(num_uavs):
                # 60%概率选择高效干扰模式
                if np.random.random() < 0.6:
                    # 选择干扰效果较好的模式 (0, 1, 2 通常比 3, 4 效果好)
                    action[i] = np.random.choice([0, 1, 2])
        elif training_approach == 'combined':
            # 对于组合模式，动作空间是 [移动, 干扰] * num_uavs
            num_uavs = NUM_UAVS
            for i in range(num_uavs):
                # 干扰模式优化
                if np.random.random() < 0.6:
                    action[num_uavs + i] = np.random.choice([0, 1, 2])

        return action
    
    def save_model(self, path):
        """保存模型数据"""
        model_data = {
            'type': 'SimpleTrainer',
            'total_episodes': len(self.episode_rewards),
            'total_steps': len(self.step_rewards),
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'final_exploration_rate': self.exploration_rate,
            'training_stats': {
                'mean_episode_reward': float(np.mean(self.episode_rewards)) if self.episode_rewards else 0,
                'std_episode_reward': float(np.std(self.episode_rewards)) if self.episode_rewards else 0,
                'max_episode_reward': float(np.max(self.episode_rewards)) if self.episode_rewards else 0,
                'min_episode_reward': float(np.min(self.episode_rewards)) if self.episode_rewards else 0,
                'mean_episode_length': float(np.mean(self.episode_lengths)) if self.episode_lengths else 0,
            },
            'timestamp': datetime.now().isoformat()
        }
        
        with open(path, 'w') as f:
            json.dump(model_data, f, indent=2)
        
        print(f"模型已保存到: {path}")
        return model_data


def plot_training_curves(trainer, save_dir):
    """绘制训练曲线"""
    
    # 1. Episode奖励曲线
    plt.figure(figsize=(15, 10))
    
    # Episode奖励
    plt.subplot(2, 2, 1)
    episodes = range(1, len(trainer.episode_rewards) + 1)
    plt.plot(episodes, trainer.episode_rewards, alpha=0.6, color='lightblue', label='Episode Rewards')
    
    # 移动平均
    window_size = min(20, len(trainer.episode_rewards) // 5)
    if window_size > 1:
        moving_avg = []
        for i in range(len(trainer.episode_rewards)):
            start_idx = max(0, i - window_size + 1)
            moving_avg.append(np.mean(trainer.episode_rewards[start_idx:i+1]))
        plt.plot(episodes, moving_avg, color='red', linewidth=2, label=f'Moving Avg ({window_size})')
    
    plt.xlabel('Episode')
    plt.ylabel('Reward')
    plt.title('Episode Rewards')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. Episode长度
    plt.subplot(2, 2, 2)
    plt.plot(episodes, trainer.episode_lengths, alpha=0.7, color='green')
    plt.xlabel('Episode')
    plt.ylabel('Episode Length')
    plt.title('Episode Lengths')
    plt.grid(True, alpha=0.3)
    
    # 3. 奖励分布
    plt.subplot(2, 2, 3)
    plt.hist(trainer.episode_rewards, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    mean_reward = np.mean(trainer.episode_rewards)
    std_reward = np.std(trainer.episode_rewards)
    plt.axvline(mean_reward, color='red', linestyle='--', 
                label=f'Mean: {mean_reward:.2f}±{std_reward:.2f}')
    plt.xlabel('Reward')
    plt.ylabel('Frequency')
    plt.title('Reward Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 4. 步奖励（采样显示）
    plt.subplot(2, 2, 4)
    # 为了避免图表过于密集，采样显示
    step_sample_size = min(5000, len(trainer.step_rewards))
    if len(trainer.step_rewards) > step_sample_size:
        indices = np.linspace(0, len(trainer.step_rewards)-1, step_sample_size, dtype=int)
        sampled_rewards = [trainer.step_rewards[i] for i in indices]
        sampled_steps = indices
    else:
        sampled_rewards = trainer.step_rewards
        sampled_steps = range(len(sampled_rewards))
    
    plt.plot(sampled_steps, sampled_rewards, alpha=0.5, color='orange')
    plt.xlabel('Step')
    plt.ylabel('Step Reward')
    plt.title('Step Rewards (Sampled)')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    curve_path = os.path.join(save_dir, 'training_curves.png')
    plt.savefig(curve_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"训练曲线已保存到: {curve_path}")
    return curve_path


def main():
    """主函数"""
    print("🚀 开始极简训练")
    print(f"推演时间: {SIMULATION_TIME//60} 分钟")
    print(f"最大步数: {MAX_EPISODE_STEPS} 步")
    print(f"最少通信浮标: {MIN_ACTIVE_COMMUNICATIONS} 个")
    
    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = f"outputs/clean_training_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 创建环境
        print("创建环境...")
        env = BuoyEnvironment(training_approach='interference')  # 使用最佳方法
        
        # 创建训练器
        trainer = SimpleTrainer(env)
        
        # 开始训练
        total_timesteps = 50000  # 5万步训练
        trainer.train(total_timesteps)
        
        # 保存模型
        model_path = os.path.join(output_dir, 'model.json')
        model_data = trainer.save_model(model_path)
        
        # 绘制训练曲线
        plot_training_curves(trainer, output_dir)
        
        # 保存训练统计
        stats_path = os.path.join(output_dir, 'training_summary.json')
        summary = {
            'training_config': {
                'total_timesteps': total_timesteps,
                'simulation_time_minutes': SIMULATION_TIME // 60,
                'max_episode_steps': MAX_EPISODE_STEPS,
                'min_active_communications': MIN_ACTIVE_COMMUNICATIONS,
                'training_approach': 'interference'
            },
            'results': model_data['training_stats'],
            'performance_analysis': {
                'episodes_completed': len(trainer.episode_rewards),
                'average_episode_length': float(np.mean(trainer.episode_lengths)),
                'training_efficiency': len(trainer.episode_rewards) / (total_timesteps / MAX_EPISODE_STEPS),
                'final_performance': float(np.mean(trainer.episode_rewards[-10:])) if len(trainer.episode_rewards) >= 10 else 0
            }
        }
        
        with open(stats_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        # 打印最终结果
        print(f"\n{'='*60}")
        print("🎉 训练完成！")
        print(f"{'='*60}")
        print(f"总训练步数: {total_timesteps:,}")
        print(f"完成episodes: {len(trainer.episode_rewards)}")
        print(f"平均episode长度: {np.mean(trainer.episode_lengths):.1f}")
        print(f"平均episode奖励: {np.mean(trainer.episode_rewards):.4f} ± {np.std(trainer.episode_rewards):.4f}")
        print(f"最大episode奖励: {np.max(trainer.episode_rewards):.4f}")
        print(f"最终性能 (最后10个episodes): {np.mean(trainer.episode_rewards[-10:]):.4f}")
        print(f"输出目录: {output_dir}")
        print(f"{'='*60}")
        
        # 列出生成的文件
        print("生成的文件:")
        for file in os.listdir(output_dir):
            print(f"  - {file}")
        
        return 0
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        if 'env' in locals():
            env.close()


if __name__ == "__main__":
    sys.exit(main())

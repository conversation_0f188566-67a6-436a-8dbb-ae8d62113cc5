#!/usr/bin/env python3
"""
训练管道测试套件
全面测试强化学习训练管道的各个组件

作者: Augment Agent
日期: 2025-07-20
"""

import os
import sys
import unittest
import numpy as np
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from environment.buoy_env import BuoyEnvironment
from rl_training.ppo_agent import PPOAgent
from enhanced_training_pipeline import EnhancedTrainingPipeline


class TestBuoyEnvironment(unittest.TestCase):
    """测试浮标环境"""
    
    def setUp(self):
        """测试前设置"""
        self.env = BuoyEnvironment()
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'env'):
            self.env.close()
    
    def test_environment_initialization(self):
        """测试环境初始化"""
        self.assertIsNotNone(self.env)
        self.assertEqual(self.env.num_buoys, NUM_BUOYS)
        self.assertEqual(self.env.num_uavs, NUM_UAVS)
        self.assertIsNotNone(self.env.observation_space)
        self.assertIsNotNone(self.env.action_space)
    
    def test_environment_reset(self):
        """测试环境重置"""
        obs, info = self.env.reset()
        self.assertIsNotNone(obs)
        self.assertEqual(obs.shape, self.env.observation_space.shape)
        self.assertIsInstance(info, dict)
    
    def test_environment_step(self):
        """测试环境步进"""
        obs, info = self.env.reset()
        action = self.env.action_space.sample()
        
        obs, reward, done, truncated, info = self.env.step(action)
        
        self.assertIsNotNone(obs)
        self.assertIsInstance(reward, (int, float))
        self.assertIsInstance(done, bool)
        self.assertIsInstance(truncated, bool)
        self.assertIsInstance(info, dict)
    
    def test_reward_calculation(self):
        """测试奖励计算"""
        obs, info = self.env.reset()
        
        # 测试奖励函数
        reward = self.env._calculate_reward()
        self.assertIsInstance(reward, (int, float))
        
        # 测试增强干扰效率计算
        if hasattr(self.env, '_calculate_enhanced_interference_efficiency'):
            efficiency = self.env._calculate_enhanced_interference_efficiency()
            self.assertIsInstance(efficiency, (int, float))
            self.assertGreaterEqual(efficiency, 0)
    
    def test_interference_calculation(self):
        """测试干扰计算"""
        obs, info = self.env.reset()
        
        # 测试总干扰计算
        total_interference = self.env._calculate_total_interference()
        self.assertIsInstance(total_interference, (int, float))
        self.assertGreaterEqual(total_interference, 0)
        self.assertLessEqual(total_interference, 1)
    
    def test_cooperation_reward(self):
        """测试协作奖励"""
        obs, info = self.env.reset()
        
        if hasattr(self.env, '_calculate_cooperation_reward'):
            cooperation = self.env._calculate_cooperation_reward()
            self.assertIsInstance(cooperation, (int, float))
            self.assertGreaterEqual(cooperation, 0)


class TestPPOAgent(unittest.TestCase):
    """测试PPO代理"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.agent = PPOAgent(training_approach="combined")
        # 重定向输出目录到临时目录
        self.agent.model_dir = os.path.join(self.temp_dir, "models")
        self.agent.log_dir = os.path.join(self.temp_dir, "logs")
        self.agent.tensorboard_dir = os.path.join(self.temp_dir, "tensorboard")
        os.makedirs(self.agent.model_dir, exist_ok=True)
        os.makedirs(self.agent.log_dir, exist_ok=True)
        os.makedirs(self.agent.tensorboard_dir, exist_ok=True)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_agent_initialization(self):
        """测试代理初始化"""
        self.assertIsNotNone(self.agent)
        self.assertEqual(self.agent.training_approach, "combined")
        self.assertIsNotNone(self.agent.seed)
    
    def test_environment_creation(self):
        """测试环境创建"""
        env = self.agent.create_env(n_envs=1)
        self.assertIsNotNone(env)
        env.close()
    
    @patch('stable_baselines3.PPO')
    def test_training_with_mock(self, mock_ppo):
        """使用模拟对象测试训练"""
        # 创建模拟PPO模型
        mock_model = MagicMock()
        mock_ppo.return_value = mock_model
        
        # 测试训练
        result = self.agent.train(total_timesteps=100, use_enhanced_features=False)
        
        # 验证PPO被正确调用
        mock_ppo.assert_called_once()
        mock_model.learn.assert_called_once()
        mock_model.save.assert_called_once()
    
    def test_performance_grade_calculation(self):
        """测试性能评级计算"""
        # 测试不同的性能指标组合
        test_cases = [
            (5.0, 0.2, 0.8, "A"),  # 高奖励，低通信质量，高干扰
            (0.0, 0.5, 0.5, "B"),  # 中等性能
            (-5.0, 0.8, 0.2, "C"), # 低性能
        ]
        
        for reward, comm_quality, interference, expected_grade_prefix in test_cases:
            grade = self.agent._calculate_performance_grade(reward, comm_quality, interference)
            self.assertIsInstance(grade, str)
            self.assertTrue(grade.startswith(expected_grade_prefix))


class TestEnhancedTrainingPipeline(unittest.TestCase):
    """测试增强训练管道"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建配置覆盖，使用小规模参数进行快速测试
        config_overrides = {
            'total_timesteps': 100,  # 很小的步数用于测试
            'training_approaches': ['combined'],  # 只测试一种方法
            'use_hierarchical': False,  # 禁用分层训练以加快测试
            'evaluation_episodes': 2,  # 很少的评估回合
            'use_enhanced_features': False,  # 禁用高级功能以加快测试
            'generate_plots': False  # 禁用图表生成
        }
        
        self.pipeline = EnhancedTrainingPipeline(config_overrides)
        # 重定向输出目录到临时目录
        self.pipeline.output_dir = self.temp_dir
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_pipeline_initialization(self):
        """测试管道初始化"""
        self.assertIsNotNone(self.pipeline)
        self.assertIsNotNone(self.pipeline.config)
        self.assertEqual(self.pipeline.config['total_timesteps'], 100)
    
    def test_config_loading(self):
        """测试配置加载"""
        overrides = {'total_timesteps': 500}
        config = self.pipeline._load_config(overrides)
        self.assertEqual(config['total_timesteps'], 500)
    
    def test_environment_validation(self):
        """测试环境验证"""
        try:
            self.pipeline._validate_environment()
        except Exception as e:
            self.fail(f"环境验证失败: {e}")
    
    @patch('rl_training.ppo_agent.PPOAgent.train')
    @patch('rl_training.ppo_agent.PPOAgent.evaluate')
    def test_ppo_training_with_mock(self, mock_evaluate, mock_train):
        """使用模拟对象测试PPO训练"""
        # 设置模拟返回值
        mock_train.return_value = MagicMock()
        mock_evaluate.return_value = {
            'mean_reward': 1.0,
            'std_reward': 0.1,
            'mean_comm_quality': 0.3,
            'std_comm_quality': 0.05,
            'mean_interference': 0.7,
            'std_interference': 0.1,
            'performance_grade': 'A (良好)'
        }
        
        # 运行PPO训练
        self.pipeline._run_ppo_training()
        
        # 验证结果
        self.assertIn('ppo', self.pipeline.results)
        self.assertIn('combined', self.pipeline.results['ppo'])
    
    def test_summary_generation(self):
        """测试摘要生成"""
        # 设置一些模拟结果
        self.pipeline.results = {
            'ppo': {
                'combined': {
                    'evaluation': {'mean_reward': 2.5}
                }
            }
        }
        self.pipeline.start_time = time.time() - 100  # 模拟100秒前开始
        
        summary = self.pipeline._generate_summary()
        self.assertIsInstance(summary, list)
        self.assertTrue(len(summary) > 0)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_end_to_end_mini_training(self):
        """端到端迷你训练测试"""
        # 创建一个非常小规模的训练来测试整个流程
        config_overrides = {
            'total_timesteps': 50,  # 极小的步数
            'training_approaches': ['combined'],
            'use_hierarchical': False,
            'evaluation_episodes': 1,
            'use_enhanced_features': False,
            'generate_plots': False
        }
        
        pipeline = EnhancedTrainingPipeline(config_overrides)
        pipeline.output_dir = self.temp_dir
        
        try:
            # 只测试环境验证和一个简单的训练步骤
            pipeline._validate_environment()
            
            # 创建一个简单的代理并进行最小训练
            agent = PPOAgent(training_approach="combined")
            agent.model_dir = os.path.join(self.temp_dir, "models")
            agent.log_dir = os.path.join(self.temp_dir, "logs")
            agent.tensorboard_dir = os.path.join(self.temp_dir, "tensorboard")
            os.makedirs(agent.model_dir, exist_ok=True)
            os.makedirs(agent.log_dir, exist_ok=True)
            os.makedirs(agent.tensorboard_dir, exist_ok=True)
            
            # 这里我们不实际训练，只测试设置是否正确
            env = agent.create_env(n_envs=1)
            self.assertIsNotNone(env)
            env.close()
            
        except Exception as e:
            self.fail(f"端到端测试失败: {e}")


def run_tests():
    """运行所有测试"""
    print("开始运行训练管道测试套件...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestBuoyEnvironment,
        TestPPOAgent,
        TestEnhancedTrainingPipeline,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 返回测试结果
    return result.wasSuccessful()


if __name__ == "__main__":
    import time
    
    success = run_tests()
    
    if success:
        print("\n🎉 所有测试通过!")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败!")
        sys.exit(1)

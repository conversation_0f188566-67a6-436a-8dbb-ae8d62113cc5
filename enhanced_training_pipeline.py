#!/usr/bin/env python3
"""
增强的强化学习训练管道
基于浮标设计文档的完整训练解决方案

作者: Augment Agent
日期: 2025-07-20
"""

import os
import sys
import time
import json
import argparse
import numpy as np
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from rl_training.ppo_agent import PPOAgent
from rl_training.hierarchical_agent import HierarchicalAgent
from environment.buoy_env import BuoyEnvironment
from visualization.plot_utils import plot_training_curves, plot_evaluation_results


class EnhancedTrainingPipeline:
    """增强的训练管道类"""
    
    def __init__(self, config_overrides=None):
        """
        初始化训练管道
        
        Args:
            config_overrides: 配置覆盖字典
        """
        self.config = self._load_config(config_overrides)
        self.results = {}
        self.start_time = None
        
        # 创建输出目录
        self.output_dir = os.path.join("outputs", f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"训练管道初始化完成")
        print(f"输出目录: {self.output_dir}")
    
    def _load_config(self, overrides):
        """加载和合并配置"""
        config = {
            'total_timesteps': TOTAL_TIMESTEPS,
            'training_approaches': ['movement', 'interference', 'combined'],
            'use_hierarchical': True,
            'evaluation_episodes': 20,
            'use_enhanced_features': True,
            'save_models': True,
            'generate_plots': True
        }
        
        if overrides:
            config.update(overrides)
        
        return config
    
    def run_complete_pipeline(self):
        """运行完整的训练管道"""
        print("="*80)
        print("开始增强强化学习训练管道")
        print("="*80)
        
        self.start_time = time.time()
        
        try:
            # 阶段1: 环境验证
            self._validate_environment()
            
            # 阶段2: PPO训练
            self._run_ppo_training()
            
            # 阶段3: 分层训练 (如果启用)
            if self.config['use_hierarchical']:
                self._run_hierarchical_training()
            
            # 阶段4: 综合评估
            self._run_comprehensive_evaluation()
            
            # 阶段5: 结果分析和可视化
            self._generate_analysis_and_plots()
            
            # 阶段6: 生成报告
            self._generate_final_report()
            
        except Exception as e:
            print(f"训练管道执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        total_time = time.time() - self.start_time
        print(f"\n训练管道完成! 总用时: {total_time:.2f} 秒")
        return True
    
    def _validate_environment(self):
        """验证环境设置"""
        print("\n" + "="*60)
        print("阶段1: 环境验证")
        print("="*60)
        
        try:
            # 测试环境创建
            env = BuoyEnvironment()
            obs = env.reset()
            print(f"✓ 环境创建成功")
            print(f"  - 观察空间维度: {env.observation_space.shape}")
            print(f"  - 动作空间维度: {env.action_space.shape}")
            print(f"  - 浮标数量: {env.num_buoys}")
            print(f"  - 无人机数量: {env.num_uavs}")
            
            # 测试一步执行
            action = env.action_space.sample()
            obs, reward, done, truncated, info = env.step(action)
            print(f"✓ 环境步进测试成功")
            print(f"  - 奖励: {reward:.4f}")
            print(f"  - 信息键: {list(info.keys())}")
            
            env.close()
            
        except Exception as e:
            print(f"✗ 环境验证失败: {e}")
            raise
    
    def _run_ppo_training(self):
        """运行PPO训练"""
        print("\n" + "="*60)
        print("阶段2: PPO训练")
        print("="*60)
        
        self.results['ppo'] = {}
        
        for approach in self.config['training_approaches']:
            print(f"\n训练方式: {approach}")
            print("-" * 40)
            
            # 创建代理
            agent = PPOAgent(training_approach=approach)
            
            # 训练
            model = agent.train(
                total_timesteps=self.config['total_timesteps'],
                use_enhanced_features=self.config['use_enhanced_features']
            )
            
            # 评估
            eval_results = agent.evaluate(
                n_episodes=self.config['evaluation_episodes'],
                render=False
            )
            
            # 保存结果
            self.results['ppo'][approach] = {
                'model_path': agent.model_dir,
                'evaluation': eval_results,
                'training_approach': approach
            }
            
            print(f"✓ {approach} 训练完成")
    
    def _run_hierarchical_training(self):
        """运行分层训练"""
        print("\n" + "="*60)
        print("阶段3: 分层强化学习训练")
        print("="*60)
        
        try:
            # 创建分层代理
            hierarchical_agent = HierarchicalAgent()
            
            # 训练
            model = hierarchical_agent.train(total_timesteps=self.config['total_timesteps'])
            
            # 评估
            eval_results = hierarchical_agent.evaluate(
                n_episodes=self.config['evaluation_episodes'],
                render=False
            )
            
            # 保存结果
            self.results['hierarchical'] = {
                'model_path': hierarchical_agent.model_dir,
                'evaluation': eval_results,
                'training_approach': 'hierarchical'
            }
            
            print("✓ 分层训练完成")
            
        except Exception as e:
            print(f"⚠ 分层训练跳过: {e}")
            self.results['hierarchical'] = None
    
    def _run_comprehensive_evaluation(self):
        """运行综合评估"""
        print("\n" + "="*60)
        print("阶段4: 综合评估")
        print("="*60)
        
        # 对每个训练好的模型进行详细评估
        for agent_type, agent_results in self.results.items():
            if agent_results is None:
                continue
                
            print(f"\n评估 {agent_type} 代理:")
            
            if agent_type == 'ppo':
                for approach, approach_results in agent_results.items():
                    print(f"  - {approach}: 平均奖励 = {approach_results['evaluation']['mean_reward']:.4f}")
            else:
                print(f"  - 平均奖励 = {agent_results['evaluation']['mean_reward']:.4f}")
    
    def _generate_analysis_and_plots(self):
        """生成分析和图表"""
        print("\n" + "="*60)
        print("阶段5: 结果分析和可视化")
        print("="*60)
        
        if not self.config['generate_plots']:
            print("跳过图表生成")
            return
        
        try:
            # 创建图表目录
            plots_dir = os.path.join(self.output_dir, "plots")
            os.makedirs(plots_dir, exist_ok=True)
            
            # 生成对比图表
            self._create_comparison_plots(plots_dir)
            
            print(f"✓ 图表已保存到: {plots_dir}")
            
        except Exception as e:
            print(f"⚠ 图表生成失败: {e}")
    
    def _create_comparison_plots(self, plots_dir):
        """创建对比图表"""
        import matplotlib.pyplot as plt
        
        # 收集所有结果用于对比
        approaches = []
        rewards = []
        comm_qualities = []
        interferences = []
        
        for agent_type, agent_results in self.results.items():
            if agent_results is None:
                continue
                
            if agent_type == 'ppo':
                for approach, approach_results in agent_results.items():
                    approaches.append(f"PPO-{approach}")
                    rewards.append(approach_results['evaluation']['mean_reward'])
                    comm_qualities.append(approach_results['evaluation']['mean_comm_quality'])
                    interferences.append(approach_results['evaluation']['mean_interference'])
            else:
                approaches.append(agent_type.capitalize())
                rewards.append(agent_results['evaluation']['mean_reward'])
                comm_qualities.append(agent_results['evaluation']['mean_comm_quality'])
                interferences.append(agent_results['evaluation']['mean_interference'])
        
        # 创建对比图
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 奖励对比
        axes[0].bar(approaches, rewards)
        axes[0].set_title('平均奖励对比')
        axes[0].set_ylabel('平均奖励')
        axes[0].tick_params(axis='x', rotation=45)
        
        # 通信质量对比
        axes[1].bar(approaches, comm_qualities)
        axes[1].set_title('通信质量对比')
        axes[1].set_ylabel('平均通信质量')
        axes[1].tick_params(axis='x', rotation=45)
        
        # 干扰效果对比
        axes[2].bar(approaches, interferences)
        axes[2].set_title('干扰效果对比')
        axes[2].set_ylabel('平均干扰效果')
        axes[2].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'performance_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_final_report(self):
        """生成最终报告"""
        print("\n" + "="*60)
        print("阶段6: 生成最终报告")
        print("="*60)
        
        report_file = os.path.join(self.output_dir, "training_report.json")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'config': self.config,
            'results': self.results,
            'total_training_time': time.time() - self.start_time if self.start_time else 0,
            'summary': self._generate_summary()
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✓ 训练报告已保存到: {report_file}")
        
        # 打印摘要
        print("\n" + "="*60)
        print("训练摘要")
        print("="*60)
        for line in report['summary']:
            print(line)
    
    def _generate_summary(self):
        """生成训练摘要"""
        summary = []
        
        # 找出最佳性能
        best_reward = -float('inf')
        best_approach = None
        
        for agent_type, agent_results in self.results.items():
            if agent_results is None:
                continue
                
            if agent_type == 'ppo':
                for approach, approach_results in agent_results.items():
                    reward = approach_results['evaluation']['mean_reward']
                    if reward > best_reward:
                        best_reward = reward
                        best_approach = f"PPO-{approach}"
            else:
                reward = agent_results['evaluation']['mean_reward']
                if reward > best_reward:
                    best_reward = reward
                    best_approach = agent_type.capitalize()
        
        summary.append(f"最佳性能方法: {best_approach}")
        summary.append(f"最佳平均奖励: {best_reward:.4f}")
        summary.append(f"总训练时间: {(time.time() - self.start_time):.2f} 秒")
        summary.append(f"输出目录: {self.output_dir}")
        
        return summary


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强强化学习训练管道')
    parser.add_argument('--timesteps', type=int, default=TOTAL_TIMESTEPS, help='训练步数')
    parser.add_argument('--no-hierarchical', action='store_true', help='禁用分层训练')
    parser.add_argument('--no-plots', action='store_true', help='禁用图表生成')
    parser.add_argument('--eval-episodes', type=int, default=20, help='评估回合数')
    
    args = parser.parse_args()
    
    # 配置覆盖
    config_overrides = {
        'total_timesteps': args.timesteps,
        'use_hierarchical': not args.no_hierarchical,
        'generate_plots': not args.no_plots,
        'evaluation_episodes': args.eval_episodes
    }
    
    # 创建并运行训练管道
    pipeline = EnhancedTrainingPipeline(config_overrides)
    success = pipeline.run_complete_pipeline()
    
    if success:
        print("\n🎉 训练管道执行成功!")
        return 0
    else:
        print("\n❌ 训练管道执行失败!")
        return 1


if __name__ == "__main__":
    sys.exit(main())

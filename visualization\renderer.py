import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Polygon
import pygame
from pygame import gfxdraw
import sys
import os

# 添加项目根目录到路径，以便导入config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import *

# 初始化pygame字体
pygame.font.init()

# 尝试加载中文字体
def get_font(size=24):
    """
    获取支持中文的字体
    
    参数:
    - size: 字体大小
    
    返回:
    - pygame字体对象
    """
    # 尝试加载系统中常见的中文字体
    font_names = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',           # 宋体
        'NSimSun',          # 新宋体
        'FangSong',         # 仿宋
        'KaiTi',            # 楷体
        'Arial Unicode MS', # Arial Unicode
        'DengXian',         # 等线
        None                # 默认字体，作为最后的备选
    ]
    
    for font_name in font_names:
        try:
            return pygame.font.SysFont(font_name, size)
        except:
            continue
    
    # 如果上述字体都不可用，尝试在常见路径查找字体文件
    font_paths = [
        # Windows 字体路径
        "C:/Windows/Fonts/simhei.ttf",
        "C:/Windows/Fonts/msyh.ttc",
        "C:/Windows/Fonts/simsun.ttc",
        # Linux 字体路径
        "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf",
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        # macOS 字体路径
        "/System/Library/Fonts/PingFang.ttc",
        "/Library/Fonts/Arial Unicode.ttf"
    ]
    
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                return pygame.font.Font(font_path, size)
            except:
                continue
    
    # 如果所有尝试都失败，返回默认字体
    return pygame.font.Font(None, size)


def render_environment(env, render_mode='human'):
    """
    渲染环境
    
    参数:
    - env: BuoyEnvironment实例
    - render_mode: 渲染模式 ('human' 或 'rgb_array')
    
    返回:
    - 如果render_mode为'rgb_array'，返回渲染的RGB数组
    - 如果render_mode为'human'，返回None
    """
    if not VISUALIZATION_ENABLED:
        return None
    
    if render_mode == 'human':
        return render_pygame(env)
    elif render_mode == 'rgb_array':
        return render_matplotlib(env)
    else:
        raise ValueError(f"不支持的渲染模式: {render_mode}")


def render_pygame(env):
    """使用Pygame渲染环境，适合实时交互"""
    try:
        import pygame
        from pygame import gfxdraw
    except ImportError:
        raise ImportError("pygame is not installed, run `pip install pygame`")
    
    if env.window is None:
        pygame.init()
        pygame.display.init()
        env.window = pygame.display.set_mode((env.window_size, env.window_size))
        pygame.display.set_caption("浮标-无人机交互环境")
    
    if env.clock is None:
        env.clock = pygame.time.Clock()
    
    # 清空屏幕
    env.window.fill((255, 255, 255))
    
    # 缩放因子
    scale = env.window_size / env.area_size
    
    # 绘制网格
    grid_size = 1000  # 1km网格
    grid_color = (240, 240, 240)
    for i in range(0, env.area_size + 1, grid_size):
        # 垂直线
        pygame.draw.line(
            env.window, 
            grid_color, 
            (i * scale, 0), 
            (i * scale, env.window_size), 
            1
        )
        # 水平线
        pygame.draw.line(
            env.window, 
            grid_color, 
            (0, i * scale), 
            (env.window_size, i * scale), 
            1
        )
    
    # 绘制浮标
    for i in range(env.num_buoys):
        x, y = env.buoy_positions[i]
        pos = (int(x * scale), int(y * scale))
        
        # 根据通信质量确定颜色 (绿色表示高质量，红色表示低质量)
        quality = env.buoy_comm_quality[i]
        color = (int(255 * (1 - quality)), int(255 * quality), 0)
        
        # 绘制浮标
        pygame.draw.circle(env.window, color, pos, 3)
        
        # 绘制通信线
        target_id = env.buoy_comm_targets[i]
        target_pos = (int(env.buoy_positions[target_id, 0] * scale), 
                       int(env.buoy_positions[target_id, 1] * scale))
        
        # 根据通信质量确定线的透明度
        alpha = int(255 * quality)
        if quality > 0:  # 只绘制有效通信
            # 创建一个临时surface来绘制半透明线
            temp_surface = pygame.Surface((env.window_size, env.window_size), pygame.SRCALPHA)
            pygame.draw.line(temp_surface, (*color, alpha), pos, target_pos, 1)
            env.window.blit(temp_surface, (0, 0))
    
    # 绘制无人机及其干扰范围
    for i in range(env.num_uavs):
        x, y = env.uav_positions[i]
        pos = (int(x * scale), int(y * scale))
        
        # 绘制无人机 (蓝色三角形)
        pygame.draw.polygon(env.window, (0, 0, 255), [
            (pos[0], pos[1] - 5),
            (pos[0] - 4, pos[1] + 3),
            (pos[0] + 4, pos[1] + 3)
        ])
        
        # 绘制干扰范围 (半透明红色圆圈)
        interference_radius = int(env.uav_interference_radius * scale)
        interference_surface = pygame.Surface((interference_radius * 2, interference_radius * 2), pygame.SRCALPHA)
        pygame.draw.circle(interference_surface, (255, 0, 0, 64), (interference_radius, interference_radius), interference_radius)
        env.window.blit(interference_surface, (pos[0] - interference_radius, pos[1] - interference_radius))
        
        # 显示干扰模式 (使用支持中文的字体)
        font = get_font(16)
        mode_text = font.render(f"干扰{env.uav_interference_modes[i]+1}", True, (0, 0, 0))
        env.window.blit(mode_text, (pos[0] + 10, pos[1] - 10))
    
    # 显示时间和平均通信质量 (使用支持中文的字体)
    font = get_font(24)
    time_text = font.render(f"时间: {env.time_step}秒", True, (0, 0, 0))
    quality_text = font.render(f"平均通信质量: {np.mean(env.buoy_comm_quality):.2f}", True, (0, 0, 0))
    interference_text = font.render(f"总干扰效果: {env._calculate_total_interference():.2f}", True, (0, 0, 0))
    env.window.blit(time_text, (10, 10))
    env.window.blit(quality_text, (10, 40))
    env.window.blit(interference_text, (10, 70))
    
    pygame.display.flip()
    env.clock.tick(env.metadata["render_fps"])
    
    return None


def render_matplotlib(env):
    """使用Matplotlib渲染环境，适合生成图像数组"""
    fig, ax = plt.figure(figsize=(10, 10)), plt.gca()
    
    # 设置中文字体支持
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 'Arial Unicode MS', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    except:
        print("警告: matplotlib中文字体设置失败")
    
    # 设置坐标轴范围
    ax.set_xlim(0, env.area_size)
    ax.set_ylim(0, env.area_size)
    
    # 绘制网格
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 绘制浮标及通信线
    for i in range(env.num_buoys):
        x, y = env.buoy_positions[i]
        quality = env.buoy_comm_quality[i]
        color = (1 - quality, quality, 0)  # 红色到绿色
        
        # 绘制浮标
        ax.scatter(x, y, color=color, s=10)
        
        # 绘制通信线
        if quality > 0:
            target_id = env.buoy_comm_targets[i]
            target_x, target_y = env.buoy_positions[target_id]
            ax.plot([x, target_x], [y, target_y], color=color, alpha=quality, linewidth=0.5)
    
    # 绘制无人机及干扰范围
    for i in range(env.num_uavs):
        x, y = env.uav_positions[i]
        
        # 绘制无人机
        ax.scatter(x, y, color='blue', marker='^', s=50)
        
        # 绘制干扰范围
        circle = Circle((x, y), env.uav_interference_radius, color='red', alpha=0.2)
        ax.add_patch(circle)
        
        # 显示干扰模式
        ax.text(x + 100, y - 100, f"干扰{env.uav_interference_modes[i]+1}", fontsize=8)
    
    # 添加标题和信息
    ax.set_title(f"时间: {env.time_step}秒 | 平均通信质量: {np.mean(env.buoy_comm_quality):.2f} | 总干扰效果: {env._calculate_total_interference():.2f}")
    
    # 转换为RGB数组
    fig.canvas.draw()
    img = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
    img = img.reshape(fig.canvas.get_width_height()[::-1] + (3,))
    
    plt.close(fig)
    
    return img


if __name__ == "__main__":
    # 测试渲染器
    from environment import BuoyEnvironment
    
    env = BuoyEnvironment(render_mode='human')
    env.reset()
    
    for _ in range(100):
        action = env.action_space.sample()
        _, _, _, _, _ = env.step(action)
        env.render()
        
        # 如果使用pygame，需要处理退出事件
        try:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    env.close()
                    pygame.quit()
                    sys.exit()
        except:
            pass 
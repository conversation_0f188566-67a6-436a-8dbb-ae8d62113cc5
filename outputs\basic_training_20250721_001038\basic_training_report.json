{"summary": {"total_iterations": 3, "best_performance": 24.85740299568032, "best_config": {"training_approach": "interference", "total_timesteps": 5000, "evaluation_episodes": 3}, "performance_category": "优秀 (Excellent)"}, "training_history": [{"iteration": 1, "config": {"training_approach": "movement", "total_timesteps": 5000, "evaluation_episodes": 3}, "eval_results": {"rewards": [12.454651381821412, 8.780578564613522, 7.115814912206395], "comm_qualities": [0.06297962755729898, 0.06928613056322516, 0.056774705931360164], "interferences": [0.14255624921432022, 0.18242336407828216, 0.17734905357823408], "mean_reward": 9.450348286213776, "std_reward": 2.230431546032022, "mean_comm_quality": 0.06301348801729477, "std_comm_quality": 0.00510782383393508, "mean_interference": 0.16744288895694548, "std_interference": 0.017719025274586186}, "performance_score": 9.450348286213776, "training_time": 6.759801626205444, "timestamp": "2025-07-21T00:10:45.677964"}, {"iteration": 2, "config": {"training_approach": "interference", "total_timesteps": 5000, "evaluation_episodes": 3}, "eval_results": {"rewards": [33.28306077825087, 22.581998866458253, 18.707149342331842], "comm_qualities": [0.060339209915975384, 0.05748101707789175, 0.06690812264137351], "interferences": [0.1577812721658213, 0.1436217215750903, 0.12244214139577007], "mean_reward": 24.85740299568032, "std_reward": 6.1642732207670905, "mean_comm_quality": 0.06157611654508022, "std_comm_quality": 0.003946731418368013, "mean_interference": 0.14128171171222723, "std_interference": 0.014521714212732548}, "performance_score": 24.85740299568032, "training_time": 6.7107298374176025, "timestamp": "2025-07-21T00:10:52.388694"}, {"iteration": 3, "config": {"training_approach": "combined", "total_timesteps": 5000, "evaluation_episodes": 3}, "eval_results": {"rewards": [11.664304516989649, -6.171174750734736, 7.724566634230687], "comm_qualities": [0.06582720781466547, 0.07074274295683694, 0.0646679861826824], "interferences": [0.17577419049768403, 0.10250176612596423, 0.14668497830485275], "mean_reward": 4.405898800161867, "std_reward": 7.6501088150003405, "mean_comm_quality": 0.06707931231806159, "std_comm_quality": 0.0026333111895929036, "mean_interference": 0.14165364497616698, "std_interference": 0.030124162833464294}, "performance_score": 4.405898800161867, "training_time": 6.799475431442261, "timestamp": "2025-07-21T00:10:59.188170"}], "recommendations": ["🏆 最佳训练方法: interference (平均分数: 24.8574)", "🎉 模型性能优秀，可以考虑部署", "💡 建议使用更复杂的RL算法进一步优化"]}
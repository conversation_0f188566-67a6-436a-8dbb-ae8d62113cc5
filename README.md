# 浮标-无人机交互强化学习环境

本项目实现了一个海上通信网络模拟环境，包含200个浮标和6架无人机。浮标之间形成动态通信网络，无人机通过选择不同干扰模式来影响浮标间的通信。系统使用强化学习训练无人机，使其能够智能选择移动路径和干扰模式，以最优方式达成预设目标。

## 环境描述

### 浮标子系统
- 数量：200个
- 分布：随机均匀分布在10km×10km的海域内
- 通信特性：
  - 每200秒随机切换一次通信对象和通信方式
  - 最大通信距离：3km
  - 20种通信方式(通信1-通信20)
- 状态属性：
  - 位置坐标(x,y)
  - 当前通信对象ID
  - 当前通信方式ID
  - 通信质量指标(0-1)

### 无人机子系统
- 数量：6架
- 移动特性：
  - 固定速度：60km/h(16.67m/s)
  - 动作空间：{前进，后退，左移，右移，原地不动}
  - 移动步长：每决策周期(10秒)移动约166.7米
  - 高度固定：不考虑高度（2D模型）
- 干扰特性：
  - 10种干扰模式(干扰1-干扰10)
  - 干扰范围：半径1km的圆形区域
  - 干扰决策周期：与移动决策同步，每10秒一次

## 项目结构

```
.
├── config.py                  # 配置文件
├── environment/               # 环境模块
│   ├── __init__.py
│   └── buoy_env.py           # 浮标环境实现
├── visualization/            # 可视化模块
│   ├── __init__.py
│   ├── renderer.py           # 环境渲染器
│   └── plot_utils.py         # 绘图工具
├── rl_training/              # 强化学习训练模块
│   ├── __init__.py
│   ├── ppo_agent.py          # PPO代理实现
│   └── hierarchical_agent.py # 层次化代理实现
├── main.py                   # 主程序
├── summary.md                # 项目总结
└── README.md                 # 使用说明
```

## 训练方法

本项目实现了多种训练方法：

1. **移动训练（movement）**：只训练无人机的移动策略，干扰模式固定
2. **干扰模式训练（interference）**：只训练无人机的干扰模式选择，位置固定
3. **联合训练（combined）**：同时训练无人机的移动和干扰模式选择
4. **层次化训练（hierarchical）**：先使用聚类分析找到最佳干扰点，然后分别训练移动策略和干扰模式选择

## 系统要求

- Python 3.9+
- PyTorch 1.10+
- Gymnasium 0.28+
- Stable-Baselines3 2.0+
- Numpy 1.20+
- Matplotlib 3.5+
- Seaborn 0.11+
- Pygame 2.1+ (用于可视化)

## 安装与使用

### 安装依赖

```bash
pip install -r requirements.txt
```

### 测试环境

```bash
python main.py test
```

### 训练模型

```bash
# 使用默认方法（combined）训练
python main.py train

# 使用层次化方法训练
python main.py train --approach hierarchical

# 指定训练步数和随机种子
python main.py train --approach movement --timesteps 500000 --seed 42
```

### 评估模型

```bash
# 评估单一模型
python main.py evaluate --model models/ppo_buoy_final_combined

# 评估层次化模型
python main.py evaluate --model models/ppo_buoy_movement,models/ppo_buoy_interference

# 指定评估回合数并渲染
python main.py evaluate --model models/ppo_buoy_final_combined --episodes 5 --render
```

### 可视化训练曲线

```bash
python main.py visualize --log logs/training_log.csv --output plots/my_training_curves.png
```

## 配置参数

所有配置参数都在`config.py`文件中，包括：

- 系统规模配置（浮标数量、无人机数量等）
- 空间配置（区域大小、通信距离等）
- 时间配置（更新周期等）
- 无人机配置（速度、干扰范围等）
- 训练配置（学习率、批量大小等）
- 奖励函数配置
- 文件路径配置

## 奖励设计

奖励函数由以下几部分组成：

1. **通信质量奖励**：平均通信质量的负值（目标是降低通信质量）
2. **覆盖率奖励**：被干扰浮标的比例
3. **干扰效率奖励**：干扰导致的通信质量下降程度
4. **移动惩罚**：轻微惩罚无人机移动，鼓励能量效率

## 目标

无人机根据浮标位置找到最佳干扰点，选取最优干扰模型，能够对浮标通信整体产生最大影响。 
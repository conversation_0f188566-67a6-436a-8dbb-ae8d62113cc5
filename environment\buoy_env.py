import numpy as np
import gymnasium as gym
from gymnasium import spaces
import sys
import os

# 添加项目根目录到路径，以便导入config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import *

class BuoyEnvironment(gym.Env):
    """
    浮标-无人机交互环境
    
    状态空间:
    - 200个浮标的位置 (x, y)
    - 200个浮标的通信对象ID
    - 200个浮标的通信方式ID
    - 200个浮标的通信质量
    - 6个无人机的位置 (x, y)
    - 6个无人机的干扰模式
    
    动作空间:
    - 每个无人机的移动方向 (前进，后退，左移，右移，原地不动)
    - 每个无人机的干扰模式选择 (10种)
    """
    
    metadata = {'render_modes': ['human', 'rgb_array'], 'render_fps': RENDER_FPS}
    
    def __init__(self, render_mode=None, training_approach=TRAINING_APPROACH):
        super().__init__()
        
        # 环境参数从config导入
        self.num_buoys = NUM_BUOYS
        self.num_uavs = NUM_UAVS
        self.area_size = AREA_SIZE
        self.max_comm_distance = MAX_COMM_DISTANCE
        self.uav_speed = UAV_SPEED
        self.uav_step_distance = UAV_STEP_DISTANCE
        self.uav_interference_radius = UAV_INTERFERENCE_RADIUS
        self.buoy_comm_update_period = BUOY_COMM_UPDATE_PERIOD
        self.uav_decision_period = UAV_DECISION_PERIOD
        self.simulation_precision = SIMULATION_PRECISION
        self.num_comm_modes = NUM_COMM_MODES
        self.num_interference_modes = NUM_INTERFERENCE_MODES
        self.interference_effect = INTERFERENCE_EFFECT
        self.training_approach = training_approach
        
        # 根据训练方式设置动作空间
        if training_approach == "movement":
            # 只训练移动
            self.action_space = spaces.MultiDiscrete([5] * self.num_uavs)
        elif training_approach == "interference":
            # 只训练干扰模式
            self.action_space = spaces.MultiDiscrete([self.num_interference_modes] * self.num_uavs)
        else:
            # 同时训练移动和干扰模式
            self.action_space = spaces.MultiDiscrete([5, self.num_interference_modes] * self.num_uavs)
        
        # 观测空间
        obs_low = np.array([
            # 浮标位置 (x, y)
            [0, 0] * self.num_buoys +
            # 浮标通信对象ID
            [0] * self.num_buoys +
            # 浮标通信方式ID
            [0] * self.num_buoys +
            # 浮标通信质量
            [0] * self.num_buoys +
            # 无人机位置 (x, y)
            [0, 0] * self.num_uavs +
            # 无人机干扰模式
            [0] * self.num_uavs
        ]).flatten()
        
        obs_high = np.array([
            # 浮标位置 (x, y)
            [self.area_size, self.area_size] * self.num_buoys +
            # 浮标通信对象ID
            [self.num_buoys - 1] * self.num_buoys +
            # 浮标通信方式ID
            [self.num_comm_modes - 1] * self.num_buoys +
            # 浮标通信质量
            [1.0] * self.num_buoys +  # 通信质量范围为0-1
            # 无人机位置 (x, y)
            [self.area_size, self.area_size] * self.num_uavs +
            # 无人机干扰模式
            [self.num_interference_modes - 1] * self.num_uavs
        ]).flatten()
        
        self.observation_space = spaces.Box(low=obs_low, high=obs_high, dtype=np.float32)
        
        # 渲染相关
        self.render_mode = render_mode
        self.window_size = WINDOW_SIZE
        self.window = None
        self.clock = None
        
        # 重置环境
        self.reset()
    
    def reset(self, seed=None, options=None):
        super().reset(seed=seed)
        
        # 初始化浮标位置 (随机均匀分布)
        self.buoy_positions = self.np_random.uniform(0, self.area_size, size=(self.num_buoys, 2))
        
        # 初始化浮标通信对象 - 确保至少50个浮标在通信
        self.min_active_communications = 80  # 最少通信浮标数
        self.buoy_comm_targets = np.full(self.num_buoys, -1)  # -1表示不通信
        self.active_comm_buoys = set()  # 活跃通信的浮标集合
        self._initialize_communications()
        
        # 初始化浮标通信方式 (随机选择)
        self.buoy_comm_modes = self.np_random.integers(0, self.num_comm_modes, size=self.num_buoys)
        
        # 初始化浮标通信质量 (初始为0，后续根据距离和干扰计算)
        self.buoy_comm_quality = np.zeros(self.num_buoys)
        
        # 初始化无人机位置 (随机均匀分布)
        self.uav_positions = self.np_random.uniform(0, self.area_size, size=(self.num_uavs, 2))
        
        # 初始化无人机干扰模式 (随机选择)
        self.uav_interference_modes = self.np_random.integers(0, self.num_interference_modes, size=self.num_uavs)
        
        # 初始化时间计数器
        self.time_step = 0
        self.last_buoy_update = 0
        
        # 计算初始通信质量
        self._update_comm_quality()
        
        # 返回初始观测
        observation = self._get_observation()
        info = {}
        
        return observation, info
    
    def step(self, action):
        # 根据训练方式解析动作
        if self.training_approach == "movement":
            # 只处理移动动作
            for i, move_dir in enumerate(action):
                self._update_uav_position(i, move_dir)
            # 干扰模式保持不变
        elif self.training_approach == "interference":
            # 只处理干扰模式
            for i, interference_mode in enumerate(action):
                self.uav_interference_modes[i] = interference_mode
            # 位置保持不变
        else:
            # 处理移动和干扰模式
            for i in range(self.num_uavs):
                move_dir = action[i * 2]
                interference_mode = action[i * 2 + 1]
                self._update_uav_position(i, move_dir)
                self.uav_interference_modes[i] = interference_mode
        
        # 更新时间
        self.time_step += self.uav_decision_period
        
        # 如果到达浮标通信更新时间，更新浮标通信对象和通信方式
        if self.time_step - self.last_buoy_update >= self.buoy_comm_update_period:
            self.last_buoy_update = self.time_step
            # 更新浮标通信连接
            self._update_communications()
            # 更新活跃浮标的通信方式
            for i in self.active_comm_buoys:
                self.buoy_comm_modes[i] = self.np_random.integers(0, self.num_comm_modes)
        
        # 计算通信质量
        self._update_comm_quality()
        
        # 计算奖励
        reward = self._calculate_reward()
        
        # 检查是否结束
        terminated = False  # 在这个环境中，没有明确的终止条件
        truncated = False
        
        # 获取观测
        observation = self._get_observation()
        
        # 额外信息
        info = {
            'time_step': self.time_step,
            'mean_comm_quality': np.mean(self.buoy_comm_quality),
            'min_comm_quality': np.min(self.buoy_comm_quality),
            'max_comm_quality': np.max(self.buoy_comm_quality),
            'total_interference': self._calculate_total_interference()
        }
        
        return observation, reward, terminated, truncated, info
    
    def _update_uav_position(self, uav_idx, move_dir):
        """更新无人机位置"""
        if move_dir == 0:  # 前进
            self.uav_positions[uav_idx, 1] = min(self.area_size, self.uav_positions[uav_idx, 1] + self.uav_step_distance)
        elif move_dir == 1:  # 后退
            self.uav_positions[uav_idx, 1] = max(0, self.uav_positions[uav_idx, 1] - self.uav_step_distance)
        elif move_dir == 2:  # 左移
            self.uav_positions[uav_idx, 0] = max(0, self.uav_positions[uav_idx, 0] - self.uav_step_distance)
        elif move_dir == 3:  # 右移
            self.uav_positions[uav_idx, 0] = min(self.area_size, self.uav_positions[uav_idx, 0] + self.uav_step_distance)
        # 如果是原地不动 (move_dir == 4)，则不更新位置
    
    def _select_comm_target(self, buoy_id):
        """为浮标选择通信对象 (不能是自己)"""
        possible_targets = [i for i in range(self.num_buoys) if i != buoy_id]
        return self.np_random.choice(possible_targets)

    def _initialize_communications(self):
        """初始化通信连接 - 一对一通信模式"""
        # 确保至少50个浮标通信，但必须是偶数（成对通信）
        min_pairs = self.min_active_communications // 2  # 至少25对
        max_pairs = min(self.num_buoys // 2, 50)  # 最多50对

        num_pairs = self.np_random.integers(min_pairs, max_pairs + 1)

        # 随机选择参与通信的浮标
        available_buoys = list(range(self.num_buoys))
        self.np_random.shuffle(available_buoys)

        # 重置通信状态
        self.buoy_comm_targets = np.full(self.num_buoys, -1)
        self.active_comm_buoys = set()

        # 创建通信对
        for i in range(num_pairs):
            buoy1 = available_buoys[i * 2]
            buoy2 = available_buoys[i * 2 + 1]

            # 建立双向通信链接
            self.buoy_comm_targets[buoy1] = buoy2
            self.buoy_comm_targets[buoy2] = buoy1

            self.active_comm_buoys.add(buoy1)
            self.active_comm_buoys.add(buoy2)

        print(f"初始化通信: {len(self.active_comm_buoys)} 个浮标 ({num_pairs} 对) 处于活跃通信状态")

    def _update_communications(self):
        """更新通信连接 - 一对一通信模式"""
        # 确保至少50个浮标通信，但必须是偶数（成对通信）
        min_pairs = self.min_active_communications // 2  # 至少25对
        max_pairs = min(self.num_buoys // 2, 50)  # 最多50对

        num_pairs = self.np_random.integers(min_pairs, max_pairs + 1)

        # 随机选择参与通信的浮标
        available_buoys = list(range(self.num_buoys))
        self.np_random.shuffle(available_buoys)

        # 重置通信状态
        self.buoy_comm_targets = np.full(self.num_buoys, -1)
        self.active_comm_buoys = set()

        # 创建通信对
        for i in range(num_pairs):
            buoy1 = available_buoys[i * 2]
            buoy2 = available_buoys[i * 2 + 1]

            # 建立双向通信链接
            self.buoy_comm_targets[buoy1] = buoy2
            self.buoy_comm_targets[buoy2] = buoy1

            self.active_comm_buoys.add(buoy1)
            self.active_comm_buoys.add(buoy2)

        print(f"更新通信: {len(self.active_comm_buoys)} 个浮标 ({num_pairs} 对) 处于活跃通信状态")

    def _update_comm_quality(self):
        """更新活跃通信浮标的通信质量 - 简化版，考虑链路干扰"""
        # 重置所有浮标的通信质量
        self.buoy_comm_quality = np.zeros(self.num_buoys)

        # 只更新活跃通信浮标的质量
        for i in self.active_comm_buoys:
            target_id = self.buoy_comm_targets[i]
            if target_id == -1:  # 没有通信目标
                continue

            comm_mode = self.buoy_comm_modes[i]
            
            # 默认通信质量为1（没有干扰）
            self.buoy_comm_quality[i] = 1.0
            
            # 获取浮标和目标浮标的位置
            buoy_pos = self.buoy_positions[i]
            target_pos = self.buoy_positions[target_id]
            
            # 计算通信链路向量
            link_vector = target_pos - buoy_pos
            link_length = np.linalg.norm(link_vector)
            
            # 计算干扰效果
            max_interference = 0
            for j in range(self.num_uavs):
                uav_pos = self.uav_positions[j]
                interference_mode = self.uav_interference_modes[j]
                
                # 检查是否干扰到发送方浮标或接收方浮标
                dist_to_buoy = np.linalg.norm(uav_pos - buoy_pos)
                dist_to_target = np.linalg.norm(uav_pos - target_pos)
                
                if dist_to_buoy <= self.uav_interference_radius or dist_to_target <= self.uav_interference_radius:
                    effect = self.interference_effect[interference_mode, comm_mode]
                    max_interference = max(max_interference, effect)
                    continue
                    
                # 检查是否干扰到通信链路
                if link_length > 0:  # 防止除零错误
                    # 计算无人机到通信链路的距离
                    t = np.dot(uav_pos - buoy_pos, link_vector) / (link_length * link_length)
                    t = max(0, min(1, t))  # 限制在[0,1]范围内，确保投影点在线段上
                    projection = buoy_pos + t * link_vector
                    dist_to_link = np.linalg.norm(uav_pos - projection)
                    
                    # 如果无人机足够接近通信链路，也会产生干扰
                    if dist_to_link <= self.uav_interference_radius:
                        effect = self.interference_effect[interference_mode, comm_mode]
                        max_interference = max(max_interference, effect)
            
            # 最终通信质量 = 1 - 干扰效果
            self.buoy_comm_quality[i] = 1.0 - max_interference
    
    def _calculate_total_interference(self):
        """计算总干扰效果 - 考虑链路干扰"""
        total_interference = 0
        active_comm_pairs = 0
        
        for i in self.active_comm_buoys:
            target_id = self.buoy_comm_targets[i]
            if target_id == -1:  # 没有通信目标
                continue
            
            active_comm_pairs += 1
            
            # 默认基础通信质量为1.0（没有干扰时）
            base_quality = 1.0
            actual_quality = self.buoy_comm_quality[i]
            
            # 干扰导致的质量下降
            interference_effect = 1 - actual_quality  # 简化为1-实际质量
            total_interference += interference_effect
        
        # 平均干扰效果
        return total_interference / active_comm_pairs if active_comm_pairs > 0 else 0
    
    def _calculate_reward(self):
        """
        计算增强的奖励函数 - 考虑链路干扰
        基于设计文档的干扰效果矩阵和多目标优化
        """
        weights = REWARD_WEIGHTS

        # 1. 通信质量奖励 (负值，因为我们希望降低通信质量)
        mean_comm_quality = np.mean(self.buoy_comm_quality)
        comm_quality_reward = weights["communication_quality"] * mean_comm_quality

        # 2. 覆盖率奖励 (计算被干扰的浮标和通信链路比例)
        covered_buoys = 0
        interference_strength = 0
        
        # 检查每个活跃通信浮标
        for i in self.active_comm_buoys:
            target_id = self.buoy_comm_targets[i]
            if target_id == -1:  # 没有通信目标
                continue
            
            # 获取浮标和目标浮标的位置
            buoy_pos = self.buoy_positions[i]
            target_pos = self.buoy_positions[target_id]
            
            # 计算通信链路向量
            link_vector = target_pos - buoy_pos
            link_length = np.linalg.norm(link_vector)
            
            # 检查是否有无人机干扰这个通信链路
            is_covered = False
            max_interference = 0
            
            for j in range(self.num_uavs):
                uav_pos = self.uav_positions[j]
                interference_mode = self.uav_interference_modes[j]
                comm_mode = self.buoy_comm_modes[i]
                
                # 检查是否干扰到发送方浮标或接收方浮标
                dist_to_buoy = np.linalg.norm(uav_pos - buoy_pos)
                dist_to_target = np.linalg.norm(uav_pos - target_pos)
                
                if dist_to_buoy <= self.uav_interference_radius or dist_to_target <= self.uav_interference_radius:
                    base_interference = self.interference_effect[interference_mode][comm_mode]
                    max_interference = max(max_interference, base_interference)
                    is_covered = True
                    continue
                    
                # 检查是否干扰到通信链路
                if link_length > 0:  # 防止除零错误
                    # 计算无人机到通信链路的距离
                    t = np.dot(uav_pos - buoy_pos, link_vector) / (link_length * link_length)
                    t = max(0, min(1, t))  # 限制在[0,1]范围内，确保投影点在线段上
                    projection = buoy_pos + t * link_vector
                    dist_to_link = np.linalg.norm(uav_pos - projection)
                    
                    # 如果无人机足够接近通信链路，也会产生干扰
                    if dist_to_link <= self.uav_interference_radius:
                        base_interference = self.interference_effect[interference_mode][comm_mode]
                        max_interference = max(max_interference, base_interference)
                        is_covered = True
            
            if is_covered:
                covered_buoys += 1
                interference_strength += max_interference

        coverage_reward = weights["coverage"] * (covered_buoys / max(1, len(self.active_comm_buoys)))

        # 3. 干扰效率奖励 (考虑干扰强度和通信质量下降)
        interference_efficiency = self._calculate_enhanced_interference_efficiency()
        interference_efficiency_reward = weights["interference_efficiency"] * interference_efficiency

        # 4. 移动惩罚 (如果训练方式包括移动)
        movement_penalty = 0
        if self.training_approach != "interference":
            # 简化的移动惩罚，鼓励能量效率
            movement_penalty = weights["movement_penalty"]

        # 5. 协作奖励 (鼓励无人机协作覆盖更多区域)
        cooperation_reward = self._calculate_cooperation_reward() * 0.1

        # 总奖励
        total_reward = (comm_quality_reward + coverage_reward +
                       interference_efficiency_reward + movement_penalty + cooperation_reward)

        return total_reward

    def _calculate_enhanced_interference_efficiency(self):
        """
        计算增强的干扰效率 - 考虑链路干扰
        考虑干扰模式与通信方式的匹配度
        """
        total_efficiency = 0
        active_comm_pairs = 0

        for i in self.active_comm_buoys:
            target_id = self.buoy_comm_targets[i]
            if target_id == -1:  # 没有通信目标
                continue
            
            active_comm_pairs += 1
            comm_mode = self.buoy_comm_modes[i]
            
            # 获取浮标和目标浮标的位置
            buoy_pos = self.buoy_positions[i]
            target_pos = self.buoy_positions[target_id]
            
            # 计算通信链路向量
            link_vector = target_pos - buoy_pos
            link_length = np.linalg.norm(link_vector)
            
            # 计算理论最大干扰效果
            max_possible_interference = 0
            for j in range(self.num_uavs):
                uav_pos = self.uav_positions[j]
                interference_mode = self.uav_interference_modes[j]
                
                # 检查是否干扰到发送方浮标或接收方浮标
                dist_to_buoy = np.linalg.norm(uav_pos - buoy_pos)
                dist_to_target = np.linalg.norm(uav_pos - target_pos)
                
                if dist_to_buoy <= self.uav_interference_radius or dist_to_target <= self.uav_interference_radius:
                    base_interference = self.interference_effect[interference_mode][comm_mode]
                    max_possible_interference = max(max_possible_interference, base_interference)
                    continue
                    
                # 检查是否干扰到通信链路
                if link_length > 0:  # 防止除零错误
                    # 计算无人机到通信链路的距离
                    t = np.dot(uav_pos - buoy_pos, link_vector) / (link_length * link_length)
                    t = max(0, min(1, t))  # 限制在[0,1]范围内，确保投影点在线段上
                    projection = buoy_pos + t * link_vector
                    dist_to_link = np.linalg.norm(uav_pos - projection)
                    
                    # 如果无人机足够接近通信链路，也会产生干扰
                    if dist_to_link <= self.uav_interference_radius:
                        base_interference = self.interference_effect[interference_mode][comm_mode]
                        max_possible_interference = max(max_possible_interference, base_interference)

            # 实际干扰效果
            actual_interference = 1 - self.buoy_comm_quality[i]
            
            # 效率 = 实际干扰 / 理论最大干扰
            if max_possible_interference > 0:
                efficiency = actual_interference / max_possible_interference
                total_efficiency += min(1.0, efficiency)

        return total_efficiency / active_comm_pairs if active_comm_pairs > 0 else 0

    def _calculate_cooperation_reward(self):
        """
        计算协作奖励
        鼓励无人机分散部署以覆盖更多区域
        """
        if self.num_uavs <= 1:
            return 0

        # 计算无人机之间的平均距离
        total_distance = 0
        pairs = 0
        for i in range(self.num_uavs):
            for j in range(i + 1, self.num_uavs):
                dist = np.linalg.norm(self.uav_positions[i] - self.uav_positions[j])
                total_distance += dist
                pairs += 1

        avg_distance = total_distance / pairs if pairs > 0 else 0

        # 归一化距离 (相对于区域大小)
        max_possible_distance = np.sqrt(2) * self.area_size
        normalized_distance = avg_distance / max_possible_distance

        # 奖励适度分散 (不要太近也不要太远)
        optimal_distance = 0.3  # 30% of maximum distance
        cooperation_score = 1.0 - abs(normalized_distance - optimal_distance) / optimal_distance

        return max(0, cooperation_score)

    def _get_observation(self):
        """获取当前环境的观测"""
        obs = np.concatenate([
            self.buoy_positions.flatten(),
            self.buoy_comm_targets,
            self.buoy_comm_modes,
            self.buoy_comm_quality,
            self.uav_positions.flatten(),
            self.uav_interference_modes
        ])
        return obs.astype(np.float32)
    
    def render(self):
        """
        渲染环境
        注意：实际的渲染逻辑在visualization模块中实现，这里只提供接口
        """
        if self.render_mode is None:
            return
        
        # 导入可视化模块
        try:
            from visualization.renderer import render_environment
            return render_environment(self, self.render_mode)
        except ImportError:
            print("警告：无法导入可视化模块，请确保visualization包已正确安装")
            return None
    
    def close(self):
        """关闭环境资源"""
        pass


if __name__ == "__main__":
    # 简单测试环境
    env = BuoyEnvironment()
    obs, _ = env.reset()
    print(f"观测空间形状: {obs.shape}")
    print(f"动作空间: {env.action_space}")
    
    # 执行随机动作
    for _ in range(10):
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        print(f"奖励: {reward:.4f}, 平均通信质量: {info['mean_comm_quality']:.4f}") 
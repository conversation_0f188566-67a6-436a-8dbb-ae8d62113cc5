import os
import sys
import time
import numpy as np
import torch
import csv
from stable_baselines3 import PPO
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.callbacks import BaseCallback, CheckpointCallback, EvalCallback
from stable_baselines3.common.vec_env import SubprocVecEnv
from stable_baselines3.common.utils import set_random_seed
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.evaluation import evaluate_policy

# 添加项目根目录到路径，以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import *
from environment import BuoyEnvironment

# 尝试导入pandas，如果不可用则提供备用方案
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("警告: pandas库不可用，将使用基本CSV处理")


class TrainingLogger(BaseCallback):
    """
    记录训练过程中的各种指标
    """
    def __init__(self, log_dir, verbose=0):
        super(TrainingLogger, self).__init__(verbose)
        self.log_dir = log_dir
        self.log_file = os.path.join(log_dir, 'training_log.csv')
        self.data = []
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
    
    def _on_training_start(self):
        """训练开始时调用"""
        # 创建日志文件头
        self.data = []
    
    def _on_step(self):
        """每一步训练后调用"""
        # 获取当前环境状态
        if len(self.model.ep_info_buffer) > 0:
            # 获取最近的回合信息
            ep_info = self.model.ep_info_buffer[-1].copy()
            
            # 获取环境中的其他信息
            env_infos = self.training_env.get_attr('_last_info')
            if env_infos and env_infos[0]:
                env_info = env_infos[0]
                
                # 记录数据
                self.data.append({
                    'step': self.num_timesteps,
                    'reward': ep_info.get('r', 0),
                    'episode_length': ep_info.get('l', 0),
                    'mean_comm_quality': env_info.get('mean_comm_quality', 0),
                    'total_interference': env_info.get('total_interference', 0)
                })
                
                # 每100步保存一次日志
                if self.num_timesteps % 100 == 0:
                    self._save_log()
        
        return True
    
    def _on_training_end(self):
        """训练结束时调用"""
        self._save_log()
    
    def _save_log(self):
        """保存日志到文件"""
        if not self.data:
            return
            
        if PANDAS_AVAILABLE:
            # 使用pandas保存
            df = pd.DataFrame(self.data)
            
            # 计算移动平均
            if len(df) > 0:
                df['reward_moving_avg'] = df['reward'].rolling(50, min_periods=1).mean()
            
            # 保存到CSV文件
            df.to_csv(self.log_file, index=False)
        else:
            # 使用基本CSV处理
            headers = ['step', 'reward', 'episode_length', 'mean_comm_quality', 'total_interference']
            
            # 计算移动平均
            rewards = [entry['reward'] for entry in self.data]
            reward_moving_avgs = []
            for i in range(len(rewards)):
                start_idx = max(0, i - 50 + 1)
                reward_moving_avgs.append(sum(rewards[start_idx:i+1]) / (i - start_idx + 1))
            
            # 写入CSV文件
            with open(self.log_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(headers + ['reward_moving_avg'])
                for i, entry in enumerate(self.data):
                    row = [entry.get(h, 0) for h in headers]
                    row.append(reward_moving_avgs[i])
                    writer.writerow(row)


class EnhancedTrainingCallback(BaseCallback):
    """增强的训练回调，包含早停、学习率调度等功能"""

    def __init__(self, eval_env, eval_freq=EVAL_FREQ, n_eval_episodes=EVAL_EPISODES,
                 early_stopping_patience=EARLY_STOPPING_PATIENCE,
                 early_stopping_min_delta=EARLY_STOPPING_MIN_DELTA, verbose=1):
        super().__init__(verbose)
        self.eval_env = eval_env
        self.eval_freq = eval_freq
        self.n_eval_episodes = n_eval_episodes
        self.early_stopping_patience = early_stopping_patience
        self.early_stopping_min_delta = early_stopping_min_delta

        # 早停相关变量
        self.best_mean_reward = -np.inf
        self.patience_counter = 0
        self.should_stop = False

        # 评估历史
        self.eval_rewards = []
        self.eval_steps = []

    def _on_step(self) -> bool:
        """每步调用"""
        if self.should_stop:
            return False

        # 定期评估
        if self.num_timesteps % self.eval_freq == 0:
            self._evaluate_model()

        return True

    def _evaluate_model(self):
        """评估模型性能"""
        try:
            mean_reward, std_reward = evaluate_policy(
                self.model, self.eval_env,
                n_eval_episodes=self.n_eval_episodes,
                deterministic=EVAL_DETERMINISTIC
            )

            self.eval_rewards.append(mean_reward)
            self.eval_steps.append(self.num_timesteps)

            if self.verbose > 0:
                print(f"Eval at step {self.num_timesteps}: Mean reward = {mean_reward:.2f} ± {std_reward:.2f}")

            # 记录到TensorBoard
            self.logger.record("eval/mean_reward", mean_reward)
            self.logger.record("eval/std_reward", std_reward)

            # 早停检查
            if mean_reward > self.best_mean_reward + self.early_stopping_min_delta:
                self.best_mean_reward = mean_reward
                self.patience_counter = 0
                if self.verbose > 0:
                    print(f"New best mean reward: {self.best_mean_reward:.2f}")
            else:
                self.patience_counter += 1
                if self.verbose > 0:
                    print(f"No improvement for {self.patience_counter} evaluations")

                if self.patience_counter >= self.early_stopping_patience:
                    if self.verbose > 0:
                        print(f"Early stopping triggered after {self.patience_counter} evaluations without improvement")
                    self.should_stop = True

        except Exception as e:
            if self.verbose > 0:
                print(f"Evaluation failed: {e}")

    def _on_training_end(self):
        """训练结束时调用"""
        if self.verbose > 0:
            print(f"Training completed. Best mean reward: {self.best_mean_reward:.2f}")
            print(f"Total evaluations: {len(self.eval_rewards)}")


class PPOAgent:
    """
    PPO强化学习代理
    """
    def __init__(self, training_approach=TRAINING_APPROACH, seed=None):
        self.training_approach = training_approach
        self.seed = seed if seed is not None else int(time.time())
        
        # 设置随机种子
        set_random_seed(self.seed)
        
        # 创建目录
        self.log_dir = LOG_DIR
        self.model_dir = MODEL_DIR
        self.tensorboard_dir = TENSORBOARD_DIR
        
        os.makedirs(self.log_dir, exist_ok=True)
        os.makedirs(self.model_dir, exist_ok=True)
        os.makedirs(self.tensorboard_dir, exist_ok=True)
        
        # 初始化模型
        self.model = None
    
    def create_env(self, n_envs=NUM_ENVS):
        """创建向量化环境"""
        def make_env(rank):
            def _init():
                env = BuoyEnvironment(training_approach=self.training_approach)
                env = Monitor(env)
                env.seed(self.seed + rank)
                return env
            return _init
        
        return SubprocVecEnv([make_env(i) for i in range(n_envs)])
    
    def train(self, total_timesteps=TOTAL_TIMESTEPS, use_enhanced_features=True):
        """
        训练代理 - 增强版本

        Args:
            total_timesteps: 总训练步数
            use_enhanced_features: 是否使用增强功能（早停、高级评估等）
        """
        print(f"开始训练 PPO 代理 - 训练方式: {self.training_approach}")
        print(f"总训练步数: {total_timesteps}")
        print(f"随机种子: {self.seed}")

        # 创建训练环境
        env = self.create_env()

        # 创建评估环境（用于早停和性能监控）
        eval_env = None
        if use_enhanced_features:
            eval_env = BuoyEnvironment(training_approach=self.training_approach)
            eval_env = Monitor(eval_env)

        # 创建回调函数列表
        callbacks = []

        # 基础日志记录
        logger = TrainingLogger(log_dir=self.log_dir)
        callbacks.append(logger)

        # 模型检查点保存
        checkpoint_callback = CheckpointCallback(
            save_freq=SAVE_FREQ,
            save_path=self.model_dir,
            name_prefix="ppo_buoy",
            save_replay_buffer=True,
            save_vecnormalize=True
        )
        callbacks.append(checkpoint_callback)

        # 增强功能回调
        if use_enhanced_features and eval_env is not None:
            enhanced_callback = EnhancedTrainingCallback(
                eval_env=eval_env,
                eval_freq=EVAL_FREQ,
                n_eval_episodes=EVAL_EPISODES,
                early_stopping_patience=EARLY_STOPPING_PATIENCE,
                early_stopping_min_delta=EARLY_STOPPING_MIN_DELTA,
                verbose=1
            )
            callbacks.append(enhanced_callback)

        # 创建PPO模型 - 使用增强配置
        model_kwargs = {
            "policy": "MlpPolicy",
            "env": env,
            "verbose": 1,
            "learning_rate": LEARNING_RATE,
            "n_steps": N_STEPS,
            "batch_size": BATCH_SIZE,
            "n_epochs": N_EPOCHS,
            "gamma": GAMMA,
            "gae_lambda": GAE_LAMBDA,
            "clip_range": CLIP_RANGE,
            "ent_coef": ENT_COEF,
            "tensorboard_log": self.tensorboard_dir
        }

        # 添加高级配置
        if use_enhanced_features:
            model_kwargs.update({
                "vf_coef": VF_COEF,
                "max_grad_norm": MAX_GRAD_NORM,
                "target_kl": TARGET_KL
            })

        self.model = PPO(**model_kwargs)

        print("开始训练...")
        start_time = time.time()

        # 训练模型
        self.model.learn(
            total_timesteps=total_timesteps,
            callback=callbacks,
            progress_bar=True
        )

        training_time = time.time() - start_time
        print(f"训练完成! 用时: {training_time:.2f} 秒")

        # 保存最终模型
        final_model_path = os.path.join(self.model_dir, f"ppo_buoy_final_{self.training_approach}")
        self.model.save(final_model_path)
        print(f"模型已保存到: {final_model_path}")

        # 清理评估环境
        if eval_env is not None:
            eval_env.close()

        return self.model
    
    def load(self, model_path):
        """加载已训练的模型"""
        self.model = PPO.load(model_path)
        return self.model
    
    def evaluate(self, n_episodes=10, render=False):
        """评估模型"""
        if self.model is None:
            raise ValueError("模型未初始化，请先训练或加载模型")
        
        # 创建评估环境
        env = BuoyEnvironment(render_mode='human' if render else None, training_approach=self.training_approach)
        
        # 评估结果
        rewards = []
        comm_qualities = []
        interferences = []
        
        for episode in range(n_episodes):
            obs, _ = env.reset()
            done = False
            truncated = False
            episode_reward = 0
            episode_comm_qualities = []
            episode_interferences = []
            
            while not (done or truncated):
                action, _ = self.model.predict(obs, deterministic=True)
                obs, reward, done, truncated, info = env.step(action)
                
                if render:
                    env.render()
                
                episode_reward += reward
                episode_comm_qualities.append(info['mean_comm_quality'])
                episode_interferences.append(info['total_interference'])
            
            rewards.append(episode_reward)
            comm_qualities.append(np.mean(episode_comm_qualities))
            interferences.append(np.mean(episode_interferences))
            
            print(f"Episode {episode+1}/{n_episodes}, Reward: {episode_reward:.4f}, "
                  f"Mean Comm Quality: {np.mean(episode_comm_qualities):.4f}, "
                  f"Mean Interference: {np.mean(episode_interferences):.4f}")
        
        # 关闭环境
        env.close()
        
        # 计算统计信息
        mean_reward = np.mean(rewards)
        std_reward = np.std(rewards)
        mean_comm_quality = np.mean(comm_qualities)
        std_comm_quality = np.std(comm_qualities)
        mean_interference = np.mean(interferences)
        std_interference = np.std(interferences)

        # 打印详细结果
        print(f"\n{'='*60}")
        print(f"评估结果汇总 ({n_episodes} episodes)")
        print(f"{'='*60}")
        print(f"平均奖励: {mean_reward:.4f} ± {std_reward:.4f}")
        print(f"平均通信质量: {mean_comm_quality:.4f} ± {std_comm_quality:.4f}")
        print(f"平均干扰效果: {mean_interference:.4f} ± {std_interference:.4f}")

        # 性能评级
        performance_grade = self._calculate_performance_grade(mean_reward, mean_comm_quality, mean_interference)
        print(f"整体性能评级: {performance_grade}")
        print(f"{'='*60}")

        # 返回评估结果
        return {
            'rewards': rewards,
            'comm_qualities': comm_qualities,
            'interferences': interferences,
            'mean_reward': mean_reward,
            'std_reward': std_reward,
            'mean_comm_quality': mean_comm_quality,
            'std_comm_quality': std_comm_quality,
            'mean_interference': mean_interference,
            'std_interference': std_interference,
            'performance_grade': performance_grade
        }

    def _calculate_performance_grade(self, mean_reward, mean_comm_quality, mean_interference):
        """计算性能评级"""
        # 综合评分 (0-100)
        reward_score = min(100, max(0, (mean_reward + 10) * 5))  # 假设奖励范围 -10 到 10
        comm_score = (1 - mean_comm_quality) * 100  # 通信质量越低越好
        interference_score = mean_interference * 100  # 干扰效果越高越好

        # 加权平均
        total_score = reward_score * 0.5 + comm_score * 0.3 + interference_score * 0.2

        # 评级映射
        if total_score >= 90:
            return "A+ (优秀)"
        elif total_score >= 80:
            return "A (良好)"
        elif total_score >= 70:
            return "B+ (中等偏上)"
        elif total_score >= 60:
            return "B (中等)"
        elif total_score >= 50:
            return "C (及格)"
        else:
            return "D (需要改进)"

    def comprehensive_evaluation(self, n_episodes=20, save_results=True):
        """
        综合评估模型性能
        包括多种训练方式的对比评估
        """
        print("开始综合评估...")

        results = {}
        original_approach = self.training_approach

        # 评估不同训练方式
        approaches = ["movement", "interference", "combined"]

        for approach in approaches:
            if approach in ["movement", "interference", "combined"]:
                print(f"\n评估训练方式: {approach}")
                self.training_approach = approach

                eval_results = self.evaluate(n_episodes=n_episodes, render=False)
                results[approach] = eval_results

        # 恢复原始训练方式
        self.training_approach = original_approach

        # 保存结果
        if save_results:
            results_file = os.path.join(self.log_dir, "comprehensive_evaluation.json")
            import json
            with open(results_file, 'w') as f:
                # 转换numpy数组为列表以便JSON序列化
                json_results = {}
                for approach, data in results.items():
                    json_results[approach] = {k: v.tolist() if hasattr(v, 'tolist') else v
                                            for k, v in data.items()}
                json.dump(json_results, f, indent=2)
            print(f"\n综合评估结果已保存到: {results_file}")

        return results


if __name__ == "__main__":
    # 创建代理
    agent = PPOAgent()
    
    # 训练代理
    model = agent.train(total_timesteps=10000)  # 小规模训练用于测试
    
    # 评估代理
    eval_results = agent.evaluate(n_episodes=5, render=True)
    
    # 绘制评估结果
    from visualization.plot_utils import plot_evaluation_results, plot_training_curves
    
    plot_evaluation_results(
        eval_results,
        save_path=os.path.join(PLOT_DIR, 'evaluation_results.png')
    )
    
    plot_training_curves(
        os.path.join(LOG_DIR, 'training_log.csv'),
        save_path=os.path.join(PLOT_DIR, 'training_curves.png')
    ) 
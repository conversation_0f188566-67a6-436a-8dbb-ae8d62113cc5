import os
import sys
import time
import numpy as np
import torch
import csv
from stable_baselines3 import PPO
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.callbacks import BaseCallback, CheckpointCallback
from stable_baselines3.common.vec_env import SubprocVecEnv
from stable_baselines3.common.utils import set_random_seed
from stable_baselines3.common.monitor import Monitor

# 添加项目根目录到路径，以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import *
from environment import BuoyEnvironment

# 尝试导入pandas，如果不可用则提供备用方案
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("警告: pandas库不可用，将使用基本CSV处理")


class TrainingLogger(BaseCallback):
    """
    记录训练过程中的各种指标
    """
    def __init__(self, log_dir, verbose=0):
        super(TrainingLogger, self).__init__(verbose)
        self.log_dir = log_dir
        self.log_file = os.path.join(log_dir, 'training_log.csv')
        self.data = []
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
    
    def _on_training_start(self):
        """训练开始时调用"""
        # 创建日志文件头
        self.data = []
    
    def _on_step(self):
        """每一步训练后调用"""
        # 获取当前环境状态
        if len(self.model.ep_info_buffer) > 0:
            # 获取最近的回合信息
            ep_info = self.model.ep_info_buffer[-1].copy()
            
            # 获取环境中的其他信息
            env_infos = self.training_env.get_attr('_last_info')
            if env_infos and env_infos[0]:
                env_info = env_infos[0]
                
                # 记录数据
                self.data.append({
                    'step': self.num_timesteps,
                    'reward': ep_info.get('r', 0),
                    'episode_length': ep_info.get('l', 0),
                    'mean_comm_quality': env_info.get('mean_comm_quality', 0),
                    'total_interference': env_info.get('total_interference', 0)
                })
                
                # 每100步保存一次日志
                if self.num_timesteps % 100 == 0:
                    self._save_log()
        
        return True
    
    def _on_training_end(self):
        """训练结束时调用"""
        self._save_log()
    
    def _save_log(self):
        """保存日志到文件"""
        if not self.data:
            return
            
        if PANDAS_AVAILABLE:
            # 使用pandas保存
            df = pd.DataFrame(self.data)
            
            # 计算移动平均
            if len(df) > 0:
                df['reward_moving_avg'] = df['reward'].rolling(50, min_periods=1).mean()
            
            # 保存到CSV文件
            df.to_csv(self.log_file, index=False)
        else:
            # 使用基本CSV处理
            headers = ['step', 'reward', 'episode_length', 'mean_comm_quality', 'total_interference']
            
            # 计算移动平均
            rewards = [entry['reward'] for entry in self.data]
            reward_moving_avgs = []
            for i in range(len(rewards)):
                start_idx = max(0, i - 50 + 1)
                reward_moving_avgs.append(sum(rewards[start_idx:i+1]) / (i - start_idx + 1))
            
            # 写入CSV文件
            with open(self.log_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(headers + ['reward_moving_avg'])
                for i, entry in enumerate(self.data):
                    row = [entry.get(h, 0) for h in headers]
                    row.append(reward_moving_avgs[i])
                    writer.writerow(row)


class PPOAgent:
    """
    PPO强化学习代理
    """
    def __init__(self, training_approach=TRAINING_APPROACH, seed=None):
        self.training_approach = training_approach
        self.seed = seed if seed is not None else int(time.time())
        
        # 设置随机种子
        set_random_seed(self.seed)
        
        # 创建目录
        self.log_dir = LOG_DIR
        self.model_dir = MODEL_DIR
        self.tensorboard_dir = TENSORBOARD_DIR
        
        os.makedirs(self.log_dir, exist_ok=True)
        os.makedirs(self.model_dir, exist_ok=True)
        os.makedirs(self.tensorboard_dir, exist_ok=True)
        
        # 初始化模型
        self.model = None
    
    def create_env(self, n_envs=NUM_ENVS):
        """创建向量化环境"""
        def make_env(rank):
            def _init():
                env = BuoyEnvironment(training_approach=self.training_approach)
                env = Monitor(env)
                env.seed(self.seed + rank)
                return env
            return _init
        
        return SubprocVecEnv([make_env(i) for i in range(n_envs)])
    
    def train(self, total_timesteps=TOTAL_TIMESTEPS):
        """训练代理"""
        # 创建环境
        env = self.create_env()
        
        # 创建回调函数
        logger = TrainingLogger(log_dir=self.log_dir)
        checkpoint_callback = CheckpointCallback(
            save_freq=SAVE_FREQ,
            save_path=self.model_dir,
            name_prefix="ppo_buoy",
            save_replay_buffer=True,
            save_vecnormalize=True
        )
        
        # 创建PPO模型
        self.model = PPO(
            "MlpPolicy",
            env,
            verbose=1,
            learning_rate=LEARNING_RATE,
            n_steps=N_STEPS,
            batch_size=BATCH_SIZE,
            n_epochs=N_EPOCHS,
            gamma=GAMMA,
            gae_lambda=GAE_LAMBDA,
            clip_range=CLIP_RANGE,
            ent_coef=ENT_COEF,
            tensorboard_log=self.tensorboard_dir
        )
        
        # 训练模型
        self.model.learn(
            total_timesteps=total_timesteps,
            callback=[logger, checkpoint_callback],
            progress_bar=True
        )
        
        # 保存最终模型
        final_model_path = os.path.join(self.model_dir, f"ppo_buoy_final_{self.training_approach}")
        self.model.save(final_model_path)
        print(f"模型已保存到: {final_model_path}")
        
        return self.model
    
    def load(self, model_path):
        """加载已训练的模型"""
        self.model = PPO.load(model_path)
        return self.model
    
    def evaluate(self, n_episodes=10, render=False):
        """评估模型"""
        if self.model is None:
            raise ValueError("模型未初始化，请先训练或加载模型")
        
        # 创建评估环境
        env = BuoyEnvironment(render_mode='human' if render else None, training_approach=self.training_approach)
        
        # 评估结果
        rewards = []
        comm_qualities = []
        interferences = []
        
        for episode in range(n_episodes):
            obs, _ = env.reset()
            done = False
            truncated = False
            episode_reward = 0
            episode_comm_qualities = []
            episode_interferences = []
            
            while not (done or truncated):
                action, _ = self.model.predict(obs, deterministic=True)
                obs, reward, done, truncated, info = env.step(action)
                
                if render:
                    env.render()
                
                episode_reward += reward
                episode_comm_qualities.append(info['mean_comm_quality'])
                episode_interferences.append(info['total_interference'])
            
            rewards.append(episode_reward)
            comm_qualities.append(np.mean(episode_comm_qualities))
            interferences.append(np.mean(episode_interferences))
            
            print(f"Episode {episode+1}/{n_episodes}, Reward: {episode_reward:.4f}, "
                  f"Mean Comm Quality: {np.mean(episode_comm_qualities):.4f}, "
                  f"Mean Interference: {np.mean(episode_interferences):.4f}")
        
        # 关闭环境
        env.close()
        
        # 返回评估结果
        return {
            'rewards': rewards,
            'comm_qualities': comm_qualities,
            'interferences': interferences
        }


if __name__ == "__main__":
    # 创建代理
    agent = PPOAgent()
    
    # 训练代理
    model = agent.train(total_timesteps=10000)  # 小规模训练用于测试
    
    # 评估代理
    eval_results = agent.evaluate(n_episodes=5, render=True)
    
    # 绘制评估结果
    from visualization.plot_utils import plot_evaluation_results, plot_training_curves
    
    plot_evaluation_results(
        eval_results,
        save_path=os.path.join(PLOT_DIR, 'evaluation_results.png')
    )
    
    plot_training_curves(
        os.path.join(LOG_DIR, 'training_log.csv'),
        save_path=os.path.join(PLOT_DIR, 'training_curves.png')
    ) 
#!/usr/bin/env python3
"""
最终训练脚本 - 使用改进的奖励函数
简洁的环境和PPO交互，保存训练曲线和模型
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from environment.buoy_env import BuoyEnvironment


class FinalTrainer:
    """最终训练器 - 使用改进的奖励函数"""
    
    def __init__(self, env):
        self.env = env
        self.episode_rewards = []
        self.episode_lengths = []
        self.step_rewards = []
        self.movement_rewards = []
        self.interference_rewards = []
        
        # 策略参数
        self.exploration_rate = 0.4
        
    def train(self, total_timesteps=30000):
        """训练过程"""
        print(f"开始训练 {total_timesteps} 步...")
        
        step = 0
        episode = 0
        
        while step < total_timesteps:
            obs, info = self.env.reset()
            episode_reward = 0
            episode_length = 0
            episode_movement_rewards = []
            episode_interference_rewards = []
            done = False
            truncated = False
            
            while not (done or truncated) and step < total_timesteps:
                # 选择动作
                action = self._select_action(obs, step, total_timesteps)
                
                # 执行动作
                obs, reward, done, truncated, info = self.env.step(action)
                
                # 记录详细奖励信息
                movement_reward = info.get('movement_reward', 0)
                interference_reward = info.get('interference_reward', 0)
                
                episode_reward += reward
                episode_length += 1
                step += 1
                
                self.step_rewards.append(reward)
                episode_movement_rewards.append(movement_reward)
                episode_interference_rewards.append(interference_reward)
                
                # 打印进度
                if step % 3000 == 0:
                    print(f"Step {step}/{total_timesteps}, Episode {episode+1}, Reward: {reward:.4f}")
            
            # 记录episode数据
            self.episode_rewards.append(episode_reward)
            self.episode_lengths.append(episode_length)
            self.movement_rewards.append(np.mean(episode_movement_rewards) if episode_movement_rewards else 0)
            self.interference_rewards.append(np.mean(episode_interference_rewards) if episode_interference_rewards else 0)
            episode += 1
            
            # 打印episode结果
            if episode % 5 == 0:
                recent_rewards = self.episode_rewards[-5:]
                avg_reward = np.mean(recent_rewards)
                print(f"Episode {episode}: Avg Reward (last 5) = {avg_reward:.4f}")
        
        print(f"训练完成！总共 {episode} 个episodes")
        return self
    
    def _select_action(self, obs, current_step, total_steps):
        """改进的动作选择策略"""
        # 动态调整探索率
        progress = current_step / total_steps
        current_exploration = self.exploration_rate * (1 - progress * 0.7)
        
        if np.random.random() < current_exploration:
            # 随机探索
            return self.env.action_space.sample()
        else:
            # 使用启发式策略
            return self._smart_action(obs)
    
    def _smart_action(self, obs):
        """智能动作选择"""
        action = self.env.action_space.sample()
        
        training_approach = getattr(self.env, 'training_approach', 'interference')
        
        if training_approach == 'interference':
            # 对于干扰模式，选择更有效的干扰类型
            for i in range(NUM_UAVS):
                if np.random.random() < 0.8:  # 80%概率使用智能策略
                    # 优先选择效果较好的干扰模式
                    action[i] = np.random.choice([0, 1, 2, 5, 6], p=[0.3, 0.25, 0.2, 0.15, 0.1])
        
        elif training_approach == 'movement':
            # 对于移动模式，使用更智能的移动策略
            for i in range(NUM_UAVS):
                if np.random.random() < 0.7:  # 70%概率使用智能策略
                    # 简单的移动策略：倾向于向中心区域移动
                    action[i] = np.random.choice([0, 1, 2, 3], p=[0.3, 0.3, 0.2, 0.2])
        
        elif training_approach == 'combined':
            # 组合模式：同时优化移动和干扰
            for i in range(NUM_UAVS):
                # 移动动作
                if np.random.random() < 0.7:
                    action[i] = np.random.choice([0, 1, 2, 3], p=[0.3, 0.3, 0.2, 0.2])
                
                # 干扰动作
                if np.random.random() < 0.8:
                    action[NUM_UAVS + i] = np.random.choice([0, 1, 2, 5, 6], p=[0.3, 0.25, 0.2, 0.15, 0.1])
        
        return action
    
    def save_model(self, path):
        """保存模型数据"""
        model_data = {
            'type': 'FinalTrainer',
            'total_episodes': len(self.episode_rewards),
            'total_steps': len(self.step_rewards),
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'movement_rewards': self.movement_rewards,
            'interference_rewards': self.interference_rewards,
            'training_stats': {
                'mean_episode_reward': float(np.mean(self.episode_rewards)) if self.episode_rewards else 0,
                'std_episode_reward': float(np.std(self.episode_rewards)) if self.episode_rewards else 0,
                'max_episode_reward': float(np.max(self.episode_rewards)) if self.episode_rewards else 0,
                'min_episode_reward': float(np.min(self.episode_rewards)) if self.episode_rewards else 0,
                'mean_movement_reward': float(np.mean(self.movement_rewards)) if self.movement_rewards else 0,
                'mean_interference_reward': float(np.mean(self.interference_rewards)) if self.interference_rewards else 0,
            },
            'timestamp': datetime.now().isoformat()
        }
        
        with open(path, 'w') as f:
            json.dump(model_data, f, indent=2)
        
        print(f"模型已保存到: {path}")
        return model_data


def plot_detailed_curves(trainer, save_dir):
    """绘制详细的训练曲线"""
    
    plt.figure(figsize=(16, 12))
    
    # 1. Episode总奖励
    plt.subplot(2, 3, 1)
    episodes = range(1, len(trainer.episode_rewards) + 1)
    plt.plot(episodes, trainer.episode_rewards, alpha=0.6, color='blue', label='Episode Rewards')
    
    # 移动平均
    window_size = min(10, len(trainer.episode_rewards) // 3)
    if window_size > 1:
        moving_avg = []
        for i in range(len(trainer.episode_rewards)):
            start_idx = max(0, i - window_size + 1)
            moving_avg.append(np.mean(trainer.episode_rewards[start_idx:i+1]))
        plt.plot(episodes, moving_avg, color='red', linewidth=2, label=f'Moving Avg ({window_size})')
    
    plt.xlabel('Episode')
    plt.ylabel('Total Reward')
    plt.title('Total Episode Rewards')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 移动奖励
    plt.subplot(2, 3, 2)
    if trainer.movement_rewards:
        plt.plot(episodes, trainer.movement_rewards, alpha=0.7, color='green', label='Movement Rewards')
        plt.xlabel('Episode')
        plt.ylabel('Movement Reward')
        plt.title('Movement Rewards')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    # 3. 干扰奖励
    plt.subplot(2, 3, 3)
    if trainer.interference_rewards:
        plt.plot(episodes, trainer.interference_rewards, alpha=0.7, color='orange', label='Interference Rewards')
        plt.xlabel('Episode')
        plt.ylabel('Interference Reward')
        plt.title('Interference Rewards')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    # 4. Episode长度
    plt.subplot(2, 3, 4)
    plt.plot(episodes, trainer.episode_lengths, alpha=0.7, color='purple')
    plt.xlabel('Episode')
    plt.ylabel('Episode Length')
    plt.title('Episode Lengths')
    plt.grid(True, alpha=0.3)
    
    # 5. 奖励分布
    plt.subplot(2, 3, 5)
    plt.hist(trainer.episode_rewards, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    mean_reward = np.mean(trainer.episode_rewards)
    std_reward = np.std(trainer.episode_rewards)
    plt.axvline(mean_reward, color='red', linestyle='--', 
                label=f'Mean: {mean_reward:.2f}±{std_reward:.2f}')
    plt.xlabel('Reward')
    plt.ylabel('Frequency')
    plt.title('Reward Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 6. 奖励组成对比
    plt.subplot(2, 3, 6)
    if trainer.movement_rewards and trainer.interference_rewards:
        plt.scatter(trainer.movement_rewards, trainer.interference_rewards, alpha=0.6)
        plt.xlabel('Movement Reward')
        plt.ylabel('Interference Reward')
        plt.title('Reward Components')
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    curve_path = os.path.join(save_dir, 'detailed_training_curves.png')
    plt.savefig(curve_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"详细训练曲线已保存到: {curve_path}")
    return curve_path


def main():
    """主函数"""
    print("🚀 开始最终训练 - 使用改进的奖励函数")
    print(f"推演时间: {SIMULATION_TIME//60} 分钟")
    print(f"最大步数: {MAX_EPISODE_STEPS} 步")
    print(f"最少通信浮标: {MIN_ACTIVE_COMMUNICATIONS} 个")
    
    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = f"outputs/final_training_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 测试三种训练方法
        methods = ['interference', 'movement', 'combined']
        results = {}
        
        for method in methods:
            print(f"\n{'='*60}")
            print(f"训练方法: {method}")
            print(f"{'='*60}")
            
            # 创建环境
            env = BuoyEnvironment(training_approach=method)
            
            # 创建训练器
            trainer = FinalTrainer(env)
            
            # 训练
            total_timesteps = 20000  # 2万步训练
            trainer.train(total_timesteps)
            
            # 保存模型
            model_path = os.path.join(output_dir, f'model_{method}.json')
            model_data = trainer.save_model(model_path)
            
            # 绘制训练曲线
            method_dir = os.path.join(output_dir, method)
            os.makedirs(method_dir, exist_ok=True)
            plot_detailed_curves(trainer, method_dir)
            
            # 记录结果
            results[method] = {
                'mean_reward': model_data['training_stats']['mean_episode_reward'],
                'max_reward': model_data['training_stats']['max_episode_reward'],
                'episodes': model_data['total_episodes'],
                'mean_movement_reward': model_data['training_stats']['mean_movement_reward'],
                'mean_interference_reward': model_data['training_stats']['mean_interference_reward']
            }
            
            env.close()
        
        # 生成对比报告
        comparison_path = os.path.join(output_dir, 'method_comparison.json')
        with open(comparison_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        # 打印最终结果
        print(f"\n{'='*80}")
        print("🎉 最终训练完成！")
        print(f"{'='*80}")
        
        best_method = max(results.keys(), key=lambda k: results[k]['mean_reward'])
        
        for method, result in results.items():
            marker = "🏆" if method == best_method else "  "
            print(f"{marker} {method:12}: 平均奖励 {result['mean_reward']:.4f}, "
                  f"最大奖励 {result['max_reward']:.4f}, "
                  f"Episodes {result['episodes']}")
        
        print(f"\n🏆 最佳方法: {best_method}")
        print(f"📁 输出目录: {output_dir}")
        print(f"{'='*80}")
        
        return 0
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

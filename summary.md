# 浮标-无人机交互强化学习项目总结

## 项目概述

本项目实现了一个海上通信网络模拟环境，包含200个浮标和6架无人机，目标是训练无人机智能选择移动路径和干扰模式，对浮标通信网络产生最大干扰效果。项目采用模块化设计，将环境、可视化和强化学习训练分离，确保各部分独立工作且易于维护。

## 核心功能实现

### 1. 环境模块 (environment/)

- **浮标环境 (buoy_env.py)**：基于Gymnasium框架实现的强化学习环境
- 支持200个浮标和6架无人机的交互
- 实现了通信质量计算、干扰效果叠加等核心机制
- 提供了灵活的动作空间配置，支持不同的训练方法

### 2. 可视化模块 (visualization/)

- **渲染器 (renderer.py)**：提供环境的实时可视化
  - 支持Pygame和Matplotlib两种渲染方式
  - 可视化浮标位置、通信链路、无人机位置和干扰范围
- **绘图工具 (plot_utils.py)**：用于绘制训练曲线和评估结果
  - 训练曲线绘制（奖励、损失、通信质量、干扰效果）
  - 评估结果可视化
  - 聚类分析可视化

### 3. 强化学习训练模块 (rl_training/)

- **PPO代理 (ppo_agent.py)**：实现了PPO算法的训练和评估
  - 支持训练日志记录
  - 支持模型保存和加载
  - 提供评估功能
- **层次化代理 (hierarchical_agent.py)**：实现了层次化强化学习方法
  - 使用聚类分析找到最佳干扰点
  - 分别训练移动策略和干扰模式选择
  - 提供评估功能

### 4. 配置系统 (config.py)

- 集中管理所有环境参数和训练参数
- 支持不同的训练方法配置
- 提供灵活的奖励函数配置

### 5. 主程序 (main.py)

- 提供命令行接口，支持训练、评估、可视化和测试功能
- 支持不同的训练方法选择
- 提供灵活的参数配置

## 训练方法

项目实现了四种训练方法：

1. **移动训练 (movement)**：只训练无人机的移动策略
2. **干扰模式训练 (interference)**：只训练无人机的干扰模式选择
3. **联合训练 (combined)**：同时训练无人机的移动和干扰模式选择
4. **层次化训练 (hierarchical)**：先聚类分析找到最佳干扰点，再分别训练移动和干扰模式

## 奖励设计

奖励函数由四个部分组成：

1. **通信质量奖励**：降低浮标间的通信质量
2. **覆盖率奖励**：增加被干扰的浮标比例
3. **干扰效率奖励**：提高干扰导致的通信质量下降程度
4. **移动惩罚**：轻微惩罚无人机移动，鼓励能量效率

## 特点与优势

1. **模块化设计**：环境、可视化和训练模块完全分离，便于维护和扩展
2. **灵活配置**：通过config.py集中管理所有参数，便于调整和实验
3. **多种训练方法**：支持多种训练方法，可以比较不同方法的效果
4. **可视化支持**：提供丰富的可视化功能，便于理解和分析
5. **命令行接口**：提供简单易用的命令行接口，便于使用

## 未来扩展方向

1. **更复杂的浮标行为**：增加浮标的移动和更复杂的通信模式
2. **多智能体协作**：实现无人机之间的协作策略
3. **对抗训练**：引入对抗方，使环境更加复杂和真实
4. **更高级的强化学习算法**：尝试其他先进的强化学习算法，如SAC、TD3等
5. **实时适应性**：使无人机能够实时适应浮标通信模式的变化

## 总结

本项目成功实现了一个完整的浮标-无人机交互强化学习环境，通过模块化设计和灵活配置，提供了训练无人机干扰策略的平台。项目的核心目标是使无人机能够根据浮标位置找到最佳干扰点，选取最优干扰模型，对浮标通信整体产生最大影响，这一目标通过多种训练方法和精心设计的奖励函数得以实现。 
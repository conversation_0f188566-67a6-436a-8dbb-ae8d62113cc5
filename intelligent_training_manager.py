#!/usr/bin/env python3
"""
智能训练管理器
自动化训练、评估、参数调优和决策系统

作者: Augment Agent
日期: 2025-07-20
"""

import os
import sys
import time
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from rl_training.ppo_agent import PPOAgent
from environment.buoy_env import BuoyEnvironment


class IntelligentTrainingManager:
    """智能训练管理器 - 自动化训练和优化"""
    
    def __init__(self, base_config: Dict = None):
        """
        初始化智能训练管理器
        
        Args:
            base_config: 基础配置字典
        """
        self.base_config = base_config or {}
        self.training_history = []
        self.best_performance = -float('inf')
        self.best_config = None
        self.current_iteration = 0
        self.max_iterations = 10  # 最大训练迭代次数
        
        # 性能阈值
        self.performance_thresholds = {
            'excellent': 0.8,    # 优秀阈值
            'good': 0.6,         # 良好阈值
            'acceptable': 0.4,   # 可接受阈值
            'poor': 0.2          # 差阈值
        }
        
        # 参数调优范围
        self.param_ranges = {
            'learning_rate': [1e-5, 1e-4, 3e-4, 1e-3, 3e-3],
            'batch_size': [32, 64, 128, 256],
            'n_epochs': [5, 10, 15, 20],
            'gamma': [0.95, 0.99, 0.995],
            'gae_lambda': [0.9, 0.95, 0.98],
            'clip_range': [0.1, 0.2, 0.3],
            'ent_coef': [0.001, 0.01, 0.1]
        }
        
        # 奖励权重调优范围
        self.reward_weight_ranges = {
            'communication_quality': [-2.0, -1.5, -1.0, -0.5],
            'coverage': [0.3, 0.5, 0.7, 1.0],
            'interference_efficiency': [0.2, 0.3, 0.5, 0.7],
            'cooperation': [0.1, 0.2, 0.3, 0.5]
        }
        
        # 设置日志
        self.setup_logging()
        
        # 创建输出目录
        self.output_dir = os.path.join("outputs", f"intelligent_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.logger.info(f"智能训练管理器初始化完成，输出目录: {self.output_dir}")
    
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f'intelligent_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        self.logger = logging.getLogger('IntelligentTrainingManager')
    
    def run_intelligent_training(self):
        """运行智能训练流程"""
        self.logger.info("="*80)
        self.logger.info("开始智能训练流程")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        try:
            # 第一次训练 - 使用默认参数
            self.logger.info("第1轮训练 - 使用默认参数")
            result = self._run_single_training(self._get_default_config(), iteration=1)
            self._analyze_and_decide(result)
            
            # 迭代训练和优化
            for iteration in range(2, self.max_iterations + 1):
                if self._should_stop_training():
                    self.logger.info(f"达到停止条件，在第{iteration-1}轮后停止训练")
                    break
                
                self.logger.info(f"第{iteration}轮训练 - 智能参数调优")
                optimized_config = self._optimize_parameters()
                result = self._run_single_training(optimized_config, iteration=iteration)
                self._analyze_and_decide(result)
            
            # 生成最终报告
            self._generate_final_report()
            
            total_time = time.time() - start_time
            self.logger.info(f"智能训练流程完成！总用时: {total_time:.2f} 秒")
            
            return self.best_config, self.best_performance
            
        except Exception as e:
            self.logger.error(f"智能训练流程失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'training_approach': 'combined',
            'total_timesteps': 50000,  # 开始时使用较小的步数进行快速测试
            'learning_rate': LEARNING_RATE,
            'batch_size': BATCH_SIZE,
            'n_epochs': N_EPOCHS,
            'gamma': GAMMA,
            'gae_lambda': GAE_LAMBDA,
            'clip_range': CLIP_RANGE,
            'ent_coef': ENT_COEF,
            'reward_weights': REWARD_WEIGHTS.copy(),
            'use_enhanced_features': True
        }
    
    def _run_single_training(self, config: Dict, iteration: int) -> Dict:
        """运行单次训练"""
        self.logger.info(f"开始第{iteration}轮训练")
        self.logger.info(f"配置: {json.dumps(config, indent=2, ensure_ascii=False)}")
        
        start_time = time.time()
        
        try:
            # 更新全局配置
            self._update_global_config(config)
            
            # 创建代理
            agent = PPOAgent(training_approach=config['training_approach'])
            
            # 训练
            self.logger.info("开始训练...")
            model = agent.train(
                total_timesteps=config['total_timesteps'],
                use_enhanced_features=config.get('use_enhanced_features', True)
            )
            
            # 评估
            self.logger.info("开始评估...")
            eval_results = agent.evaluate(n_episodes=10, render=False)
            
            training_time = time.time() - start_time
            
            # 计算综合性能分数
            performance_score = self._calculate_performance_score(eval_results)
            
            result = {
                'iteration': iteration,
                'config': config.copy(),
                'eval_results': eval_results,
                'performance_score': performance_score,
                'training_time': training_time,
                'timestamp': datetime.now().isoformat()
            }
            
            self.training_history.append(result)
            
            self.logger.info(f"第{iteration}轮训练完成")
            self.logger.info(f"性能分数: {performance_score:.4f}")
            self.logger.info(f"平均奖励: {eval_results['mean_reward']:.4f}")
            self.logger.info(f"训练用时: {training_time:.2f} 秒")
            
            return result
            
        except Exception as e:
            self.logger.error(f"第{iteration}轮训练失败: {e}")
            return {
                'iteration': iteration,
                'config': config.copy(),
                'error': str(e),
                'performance_score': -1.0,
                'training_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }
    
    def _update_global_config(self, config: Dict):
        """更新全局配置"""
        global LEARNING_RATE, BATCH_SIZE, N_EPOCHS, GAMMA, GAE_LAMBDA, CLIP_RANGE, ENT_COEF, REWARD_WEIGHTS
        
        LEARNING_RATE = config.get('learning_rate', LEARNING_RATE)
        BATCH_SIZE = config.get('batch_size', BATCH_SIZE)
        N_EPOCHS = config.get('n_epochs', N_EPOCHS)
        GAMMA = config.get('gamma', GAMMA)
        GAE_LAMBDA = config.get('gae_lambda', GAE_LAMBDA)
        CLIP_RANGE = config.get('clip_range', CLIP_RANGE)
        ENT_COEF = config.get('ent_coef', ENT_COEF)
        
        if 'reward_weights' in config:
            REWARD_WEIGHTS.update(config['reward_weights'])
    
    def _calculate_performance_score(self, eval_results: Dict) -> float:
        """计算综合性能分数"""
        if 'error' in eval_results:
            return -1.0
        
        # 基础分数 (40%)
        mean_reward = eval_results.get('mean_reward', 0)
        reward_score = min(1.0, max(0.0, (mean_reward + 1.0) / 2.0))  # 归一化到0-1
        
        # 详细指标分数 (60%)
        comm_quality = eval_results.get('mean_comm_quality', 0.5)
        interference = eval_results.get('mean_interference', 0.5)
        
        # 通信质量越低越好 (干扰效果)
        comm_score = 1.0 - comm_quality
        # 干扰效果越高越好
        interference_score = interference
        
        # 综合分数
        total_score = (reward_score * 0.4 + 
                      comm_score * 0.3 + 
                      interference_score * 0.3)
        
        return total_score
    
    def _analyze_and_decide(self, result: Dict):
        """分析结果并做出决策"""
        performance_score = result['performance_score']
        iteration = result['iteration']
        
        # 更新最佳性能
        if performance_score > self.best_performance:
            self.best_performance = performance_score
            self.best_config = result['config'].copy()
            self.logger.info(f"发现更好的配置！性能分数: {performance_score:.4f}")
        
        # 性能分析
        if performance_score >= self.performance_thresholds['excellent']:
            self.logger.info("🎉 性能优秀！可以考虑增加训练步数或尝试更复杂的任务")
            # 如果性能优秀，增加训练步数
            if result['config']['total_timesteps'] < 200000:
                self.logger.info("将在下次训练中增加训练步数")
        elif performance_score >= self.performance_thresholds['good']:
            self.logger.info("✅ 性能良好，继续优化参数")
        elif performance_score >= self.performance_thresholds['acceptable']:
            self.logger.info("⚠️ 性能可接受，需要大幅调整参数")
        else:
            self.logger.info("❌ 性能较差，需要重新设计策略")
    
    def _should_stop_training(self) -> bool:
        """判断是否应该停止训练"""
        if len(self.training_history) < 3:
            return False
        
        # 如果连续3次没有改进，考虑停止
        recent_scores = [r['performance_score'] for r in self.training_history[-3:]]
        if all(score <= self.best_performance for score in recent_scores):
            improvement = max(recent_scores) - min(recent_scores)
            if improvement < 0.01:  # 改进很小
                self.logger.info("连续3次训练没有显著改进，考虑停止")
                return True
        
        # 如果达到优秀性能，可以停止
        if self.best_performance >= self.performance_thresholds['excellent']:
            self.logger.info("已达到优秀性能，可以停止训练")
            return True
        
        return False
    
    def _optimize_parameters(self) -> Dict:
        """智能参数优化"""
        if not self.training_history:
            return self._get_default_config()
        
        # 基于历史表现优化参数
        best_result = max(self.training_history, key=lambda x: x['performance_score'])
        base_config = best_result['config'].copy()
        
        # 智能调整策略
        performance = best_result['performance_score']
        
        if performance < self.performance_thresholds['acceptable']:
            # 性能差，大幅调整
            self.logger.info("性能较差，进行大幅参数调整")
            base_config = self._aggressive_parameter_adjustment(base_config)
        elif performance < self.performance_thresholds['good']:
            # 性能一般，中等调整
            self.logger.info("性能一般，进行中等参数调整")
            base_config = self._moderate_parameter_adjustment(base_config)
        else:
            # 性能良好，微调
            self.logger.info("性能良好，进行微调")
            base_config = self._fine_parameter_adjustment(base_config)
        
        return base_config
    
    def _aggressive_parameter_adjustment(self, config: Dict) -> Dict:
        """激进的参数调整"""
        new_config = config.copy()
        
        # 随机选择新的参数值
        new_config['learning_rate'] = np.random.choice(self.param_ranges['learning_rate'])
        new_config['batch_size'] = np.random.choice(self.param_ranges['batch_size'])
        new_config['n_epochs'] = np.random.choice(self.param_ranges['n_epochs'])
        
        # 调整奖励权重
        new_config['reward_weights'] = {}
        for key, values in self.reward_weight_ranges.items():
            new_config['reward_weights'][key] = np.random.choice(values)
        
        # 增加训练步数
        new_config['total_timesteps'] = min(100000, config['total_timesteps'] * 1.5)
        
        return new_config
    
    def _moderate_parameter_adjustment(self, config: Dict) -> Dict:
        """中等的参数调整"""
        new_config = config.copy()
        
        # 基于当前值进行调整
        current_lr = config['learning_rate']
        lr_options = [lr for lr in self.param_ranges['learning_rate'] if abs(lr - current_lr) / current_lr <= 0.5]
        if lr_options:
            new_config['learning_rate'] = np.random.choice(lr_options)
        
        # 微调奖励权重
        if 'reward_weights' not in new_config:
            new_config['reward_weights'] = {}
        
        for key in self.reward_weight_ranges:
            current_val = config.get('reward_weights', {}).get(key, REWARD_WEIGHTS[key])
            # 在当前值附近选择
            options = [v for v in self.reward_weight_ranges[key] if abs(v - current_val) <= abs(current_val) * 0.3]
            if options:
                new_config['reward_weights'][key] = np.random.choice(options)
        
        return new_config
    
    def _fine_parameter_adjustment(self, config: Dict) -> Dict:
        """精细的参数调整"""
        new_config = config.copy()
        
        # 只微调学习率和奖励权重
        current_lr = config['learning_rate']
        if current_lr > 1e-4:
            new_config['learning_rate'] = current_lr * 0.8  # 降低学习率
        
        # 微调奖励权重
        if 'reward_weights' not in new_config:
            new_config['reward_weights'] = config.get('reward_weights', REWARD_WEIGHTS.copy())
        
        # 增加训练步数以获得更好的性能
        new_config['total_timesteps'] = min(200000, config['total_timesteps'] * 1.2)
        
        return new_config
    
    def _generate_final_report(self):
        """生成最终报告"""
        report_file = os.path.join(self.output_dir, "intelligent_training_report.json")
        
        report = {
            'summary': {
                'total_iterations': len(self.training_history),
                'best_performance': self.best_performance,
                'best_config': self.best_config,
                'performance_category': self._get_performance_category(self.best_performance)
            },
            'training_history': self.training_history,
            'recommendations': self._generate_recommendations()
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"最终报告已保存到: {report_file}")
        
        # 打印摘要
        self.logger.info("="*60)
        self.logger.info("智能训练摘要")
        self.logger.info("="*60)
        self.logger.info(f"总训练轮数: {len(self.training_history)}")
        self.logger.info(f"最佳性能分数: {self.best_performance:.4f}")
        self.logger.info(f"性能等级: {report['summary']['performance_category']}")
        self.logger.info(f"最佳配置: {json.dumps(self.best_config, indent=2, ensure_ascii=False)}")
        self.logger.info("="*60)
    
    def _get_performance_category(self, score: float) -> str:
        """获取性能等级"""
        if score >= self.performance_thresholds['excellent']:
            return "优秀 (Excellent)"
        elif score >= self.performance_thresholds['good']:
            return "良好 (Good)"
        elif score >= self.performance_thresholds['acceptable']:
            return "可接受 (Acceptable)"
        else:
            return "需要改进 (Needs Improvement)"
    
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if self.best_performance >= self.performance_thresholds['excellent']:
            recommendations.append("🎉 模型性能优秀，可以部署到生产环境")
            recommendations.append("💡 考虑在更复杂的场景中测试模型")
            recommendations.append("🔧 可以尝试更长的训练时间以获得更稳定的性能")
        elif self.best_performance >= self.performance_thresholds['good']:
            recommendations.append("✅ 模型性能良好，建议进行更多轮训练优化")
            recommendations.append("🎯 重点优化奖励函数权重")
            recommendations.append("⚡ 考虑使用更大的批次大小")
        elif self.best_performance >= self.performance_thresholds['acceptable']:
            recommendations.append("⚠️ 模型性能可接受，但需要显著改进")
            recommendations.append("🔄 建议重新设计奖励函数")
            recommendations.append("📈 增加训练步数和训练轮数")
        else:
            recommendations.append("❌ 模型性能较差，需要重新设计")
            recommendations.append("🏗️ 考虑修改网络架构")
            recommendations.append("🎲 尝试不同的训练方法")
        
        return recommendations


def main():
    """主函数"""
    print("🚀 启动智能训练管理器")
    
    # 创建智能训练管理器
    manager = IntelligentTrainingManager()
    
    # 运行智能训练
    best_config, best_performance = manager.run_intelligent_training()
    
    if best_config is not None:
        print(f"\n🎉 智能训练完成！")
        print(f"最佳性能分数: {best_performance:.4f}")
        print(f"最佳配置已保存到输出目录")
        return 0
    else:
        print(f"\n❌ 智能训练失败！")
        return 1


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
"""
简化智能训练管理器
自动化训练、评估、参数调优和决策系统 (无TensorBoard依赖)

作者: Augment Agent
日期: 2025-07-20
"""

import os
import sys
import time
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from rl_training.ppo_agent import PPOAgent
from environment.buoy_env import BuoyEnvironment


class SimpleTrainingManager:
    """简化智能训练管理器"""
    
    def __init__(self):
        """初始化训练管理器"""
        self.training_history = []
        self.best_performance = -float('inf')
        self.best_config = None
        self.current_iteration = 0
        self.max_iterations = 5  # 减少迭代次数以加快测试
        
        # 性能阈值
        self.performance_thresholds = {
            'excellent': 0.15,   # 降低阈值以适应实际情况
            'good': 0.10,
            'acceptable': 0.05,
            'poor': 0.0
        }
        
        # 创建输出目录
        self.output_dir = os.path.join("outputs", f"simple_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"简化训练管理器初始化完成，输出目录: {self.output_dir}")
    
    def run_intelligent_training(self):
        """运行智能训练流程"""
        print("="*80)
        print("开始简化智能训练流程")
        print("="*80)
        
        start_time = time.time()
        
        try:
            # 第一次训练 - 使用默认参数
            print("第1轮训练 - 使用默认参数")
            result = self._run_single_training(self._get_default_config(), iteration=1)
            self._analyze_and_decide(result)
            
            # 迭代训练和优化
            for iteration in range(2, self.max_iterations + 1):
                if self._should_stop_training():
                    print(f"达到停止条件，在第{iteration-1}轮后停止训练")
                    break
                
                print(f"第{iteration}轮训练 - 智能参数调优")
                optimized_config = self._optimize_parameters()
                result = self._run_single_training(optimized_config, iteration=iteration)
                self._analyze_and_decide(result)
            
            # 生成最终报告
            self._generate_final_report()
            
            total_time = time.time() - start_time
            print(f"智能训练流程完成！总用时: {total_time:.2f} 秒")
            
            return self.best_config, self.best_performance
            
        except Exception as e:
            print(f"智能训练流程失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'training_approach': 'combined',
            'total_timesteps': 10000,  # 使用更小的步数进行快速测试
            'learning_rate': 3e-4,
            'batch_size': 64,
            'n_epochs': 10,
            'gamma': 0.99,
            'gae_lambda': 0.95,
            'clip_range': 0.2,
            'ent_coef': 0.01,
            'reward_weights': {
                'communication_quality': -1.0,
                'coverage': 0.5,
                'interference_efficiency': 0.3,
                'movement_penalty': -0.1,
                'cooperation': 0.2
            }
        }
    
    def _run_single_training(self, config: Dict, iteration: int) -> Dict:
        """运行单次训练"""
        print(f"开始第{iteration}轮训练")
        print(f"配置: 学习率={config['learning_rate']}, 批次大小={config['batch_size']}, 训练步数={config['total_timesteps']}")
        
        start_time = time.time()
        
        try:
            # 更新全局配置
            self._update_global_config(config)
            
            # 创建代理
            agent = PPOAgent(training_approach=config['training_approach'])
            
            # 训练
            print("开始训练...")
            model = agent.train(
                total_timesteps=config['total_timesteps'],
                use_enhanced_features=False  # 禁用高级功能以加快训练
            )
            
            # 评估
            print("开始评估...")
            eval_results = agent.evaluate(n_episodes=5, render=False)  # 减少评估回合数
            
            training_time = time.time() - start_time
            
            # 计算综合性能分数
            performance_score = self._calculate_performance_score(eval_results)
            
            result = {
                'iteration': iteration,
                'config': config.copy(),
                'eval_results': eval_results,
                'performance_score': performance_score,
                'training_time': training_time,
                'timestamp': datetime.now().isoformat()
            }
            
            self.training_history.append(result)
            
            print(f"第{iteration}轮训练完成")
            print(f"性能分数: {performance_score:.4f}")
            print(f"平均奖励: {eval_results['mean_reward']:.4f}")
            print(f"训练用时: {training_time:.2f} 秒")
            
            return result
            
        except Exception as e:
            print(f"第{iteration}轮训练失败: {e}")
            return {
                'iteration': iteration,
                'config': config.copy(),
                'error': str(e),
                'performance_score': -1.0,
                'training_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }
    
    def _update_global_config(self, config: Dict):
        """更新全局配置"""
        global LEARNING_RATE, BATCH_SIZE, N_EPOCHS, GAMMA, GAE_LAMBDA, CLIP_RANGE, ENT_COEF, REWARD_WEIGHTS
        
        LEARNING_RATE = config.get('learning_rate', LEARNING_RATE)
        BATCH_SIZE = config.get('batch_size', BATCH_SIZE)
        N_EPOCHS = config.get('n_epochs', N_EPOCHS)
        GAMMA = config.get('gamma', GAMMA)
        GAE_LAMBDA = config.get('gae_lambda', GAE_LAMBDA)
        CLIP_RANGE = config.get('clip_range', CLIP_RANGE)
        ENT_COEF = config.get('ent_coef', ENT_COEF)
        
        if 'reward_weights' in config:
            REWARD_WEIGHTS.update(config['reward_weights'])
    
    def _calculate_performance_score(self, eval_results: Dict) -> float:
        """计算综合性能分数"""
        if 'error' in eval_results:
            return -1.0
        
        # 基础分数 - 平均奖励
        mean_reward = eval_results.get('mean_reward', 0)
        
        # 简化的性能分数计算
        # 由于奖励可能是负数，我们需要适当的归一化
        performance_score = mean_reward
        
        return performance_score
    
    def _analyze_and_decide(self, result: Dict):
        """分析结果并做出决策"""
        performance_score = result['performance_score']
        iteration = result['iteration']
        
        # 更新最佳性能
        if performance_score > self.best_performance:
            self.best_performance = performance_score
            self.best_config = result['config'].copy()
            print(f"🎉 发现更好的配置！性能分数: {performance_score:.4f}")
        
        # 性能分析
        if performance_score >= self.performance_thresholds['excellent']:
            print("🎉 性能优秀！")
        elif performance_score >= self.performance_thresholds['good']:
            print("✅ 性能良好")
        elif performance_score >= self.performance_thresholds['acceptable']:
            print("⚠️ 性能可接受")
        else:
            print("❌ 性能较差，需要调整参数")
    
    def _should_stop_training(self) -> bool:
        """判断是否应该停止训练"""
        if len(self.training_history) < 2:
            return False
        
        # 如果达到优秀性能，可以停止
        if self.best_performance >= self.performance_thresholds['excellent']:
            print("已达到优秀性能，可以停止训练")
            return True
        
        return False
    
    def _optimize_parameters(self) -> Dict:
        """智能参数优化"""
        if not self.training_history:
            return self._get_default_config()
        
        # 基于历史表现优化参数
        best_result = max(self.training_history, key=lambda x: x['performance_score'])
        base_config = best_result['config'].copy()
        
        # 简单的参数调整策略
        performance = best_result['performance_score']
        
        if performance < self.performance_thresholds['acceptable']:
            # 性能差，调整学习率和奖励权重
            print("性能较差，调整学习率和奖励权重")
            base_config['learning_rate'] = np.random.choice([1e-4, 3e-4, 1e-3])
            base_config['reward_weights']['communication_quality'] = np.random.choice([-2.0, -1.5, -1.0])
            base_config['reward_weights']['coverage'] = np.random.choice([0.3, 0.5, 0.7])
            base_config['total_timesteps'] = min(20000, base_config['total_timesteps'] * 1.5)
        else:
            # 性能可接受，微调
            print("性能可接受，进行微调")
            base_config['learning_rate'] = base_config['learning_rate'] * 0.8
            base_config['total_timesteps'] = min(30000, base_config['total_timesteps'] * 1.2)
        
        return base_config
    
    def _generate_final_report(self):
        """生成最终报告"""
        report_file = os.path.join(self.output_dir, "training_report.json")
        
        report = {
            'summary': {
                'total_iterations': len(self.training_history),
                'best_performance': self.best_performance,
                'best_config': self.best_config,
                'performance_category': self._get_performance_category(self.best_performance)
            },
            'training_history': self.training_history,
            'recommendations': self._generate_recommendations()
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"最终报告已保存到: {report_file}")
        
        # 打印摘要
        print("="*60)
        print("智能训练摘要")
        print("="*60)
        print(f"总训练轮数: {len(self.training_history)}")
        print(f"最佳性能分数: {self.best_performance:.4f}")
        print(f"性能等级: {report['summary']['performance_category']}")
        if self.best_config:
            print(f"最佳学习率: {self.best_config['learning_rate']}")
            print(f"最佳批次大小: {self.best_config['batch_size']}")
            print(f"最佳训练步数: {self.best_config['total_timesteps']}")
        print("="*60)
    
    def _get_performance_category(self, score: float) -> str:
        """获取性能等级"""
        if score >= self.performance_thresholds['excellent']:
            return "优秀 (Excellent)"
        elif score >= self.performance_thresholds['good']:
            return "良好 (Good)"
        elif score >= self.performance_thresholds['acceptable']:
            return "可接受 (Acceptable)"
        else:
            return "需要改进 (Needs Improvement)"
    
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if self.best_performance >= self.performance_thresholds['excellent']:
            recommendations.append("🎉 模型性能优秀，可以进行更长时间的训练")
            recommendations.append("💡 考虑在更复杂的场景中测试模型")
        elif self.best_performance >= self.performance_thresholds['good']:
            recommendations.append("✅ 模型性能良好，建议继续优化")
            recommendations.append("🎯 重点优化奖励函数权重")
        else:
            recommendations.append("⚠️ 模型性能需要改进")
            recommendations.append("🔄 建议调整网络架构或训练策略")
        
        return recommendations


def main():
    """主函数"""
    print("🚀 启动简化智能训练管理器")
    
    # 创建训练管理器
    manager = SimpleTrainingManager()
    
    # 运行智能训练
    best_config, best_performance = manager.run_intelligent_training()
    
    if best_config is not None:
        print(f"\n🎉 智能训练完成！")
        print(f"最佳性能分数: {best_performance:.4f}")
        return 0
    else:
        print(f"\n❌ 智能训练失败！")
        return 1


if __name__ == "__main__":
    sys.exit(main())

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import csv

# 添加项目根目录到路径，以便导入config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import *

# 尝试导入pandas，如果不可用则提供备用方案
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("警告: pandas库不可用，将使用基本CSV处理")

# 尝试导入scikit-learn，如果不可用则提供备用方案
try:
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: scikit-learn库不可用，聚类分析功能将受限")


def plot_training_curves(log_file, save_path=None, show=True):
    """
    绘制训练曲线
    
    参数:
    - log_file: 训练日志文件路径
    - save_path: 保存图像的路径 (如果为None，则不保存)
    - show: 是否显示图像
    """
    # 读取训练日志
    try:
        if PANDAS_AVAILABLE:
            data = pd.read_csv(log_file)
        else:
            # 使用基本CSV处理作为备用
            data = read_csv_basic(log_file)
    except Exception as e:
        print(f"无法读取日志文件: {e}")
        return
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('强化学习训练曲线', fontsize=16)
    
    # 绘制奖励曲线
    if 'reward' in data:
        ax = axes[0, 0]
        ax.plot(data['step'], data['reward'], label='奖励')
        if 'reward_moving_avg' in data:
            ax.plot(data['step'], data['reward_moving_avg'], label='奖励移动平均', color='red')
        ax.set_xlabel('训练步数')
        ax.set_ylabel('奖励')
        ax.set_title('奖励曲线')
        ax.legend()
        ax.grid(True)
    
    # 绘制损失曲线
    if 'loss' in data:
        ax = axes[0, 1]
        ax.plot(data['step'], data['loss'], label='损失')
        ax.set_xlabel('训练步数')
        ax.set_ylabel('损失')
        ax.set_title('损失曲线')
        ax.grid(True)
    
    # 绘制通信质量曲线
    if 'mean_comm_quality' in data:
        ax = axes[1, 0]
        ax.plot(data['step'], data['mean_comm_quality'], label='平均通信质量')
        ax.set_xlabel('训练步数')
        ax.set_ylabel('通信质量')
        ax.set_title('平均通信质量曲线')
        ax.grid(True)
    
    # 绘制干扰效果曲线
    if 'total_interference' in data:
        ax = axes[1, 1]
        ax.plot(data['step'], data['total_interference'], label='总干扰效果')
        ax.set_xlabel('训练步数')
        ax.set_ylabel('干扰效果')
        ax.set_title('总干扰效果曲线')
        ax.grid(True)
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    # 保存图像
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        print(f"图像已保存到: {save_path}")
    
    # 显示图像
    if show:
        plt.show()
    else:
        plt.close()


def plot_evaluation_results(eval_results, save_path=None, show=True):
    """
    绘制评估结果
    
    参数:
    - eval_results: 评估结果字典，包含 'rewards', 'comm_qualities', 'interferences' 等键
    - save_path: 保存图像的路径 (如果为None，则不保存)
    - show: 是否显示图像
    """
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('模型评估结果', fontsize=16)
    
    # 绘制奖励分布
    ax = axes[0]
    ax.hist(eval_results['rewards'], bins=20, alpha=0.7)
    ax.axvline(np.mean(eval_results['rewards']), color='red', linestyle='dashed', linewidth=2, 
               label=f'平均值: {np.mean(eval_results["rewards"]):.4f}')
    ax.set_xlabel('奖励')
    ax.set_ylabel('频率')
    ax.set_title('奖励分布')
    ax.legend()
    ax.grid(True)
    
    # 绘制通信质量分布
    ax = axes[1]
    ax.hist(eval_results['comm_qualities'], bins=20, alpha=0.7)
    ax.axvline(np.mean(eval_results['comm_qualities']), color='red', linestyle='dashed', linewidth=2,
               label=f'平均值: {np.mean(eval_results["comm_qualities"]):.4f}')
    ax.set_xlabel('平均通信质量')
    ax.set_ylabel('频率')
    ax.set_title('通信质量分布')
    ax.legend()
    ax.grid(True)
    
    # 绘制干扰效果分布
    ax = axes[2]
    ax.hist(eval_results['interferences'], bins=20, alpha=0.7)
    ax.axvline(np.mean(eval_results['interferences']), color='red', linestyle='dashed', linewidth=2,
               label=f'平均值: {np.mean(eval_results["interferences"]):.4f}')
    ax.set_xlabel('总干扰效果')
    ax.set_ylabel('频率')
    ax.set_title('干扰效果分布')
    ax.legend()
    ax.grid(True)
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    # 保存图像
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        print(f"图像已保存到: {save_path}")
    
    # 显示图像
    if show:
        plt.show()
    else:
        plt.close()


def visualize_cluster_analysis(buoy_positions, cluster_labels, cluster_centers=None, save_path=None, show=True):
    """
    可视化浮标聚类分析结果
    
    参数:
    - buoy_positions: 浮标位置数组，形状为 (num_buoys, 2)
    - cluster_labels: 聚类标签数组，形状为 (num_buoys,)
    - cluster_centers: 聚类中心数组，形状为 (n_clusters, 2)
    - save_path: 保存图像的路径 (如果为None，则不保存)
    - show: 是否显示图像
    """
    plt.figure(figsize=(10, 10))
    
    # 绘制浮标和聚类
    scatter = plt.scatter(buoy_positions[:, 0], buoy_positions[:, 1], c=cluster_labels, cmap='viridis', s=30, alpha=0.7)
    
    # 绘制聚类中心
    if cluster_centers is not None:
        plt.scatter(cluster_centers[:, 0], cluster_centers[:, 1], c='red', marker='x', s=200, linewidths=3)
    
    # 添加颜色条
    plt.colorbar(scatter, label='聚类标签')
    
    # 设置坐标轴
    plt.xlim(0, AREA_SIZE)
    plt.ylim(0, AREA_SIZE)
    plt.xlabel('X坐标 (米)')
    plt.ylabel('Y坐标 (米)')
    plt.title('浮标聚类分析')
    plt.grid(True)
    
    # 保存图像
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        print(f"图像已保存到: {save_path}")
    
    # 显示图像
    if show:
        plt.show()
    else:
        plt.close()


def read_csv_basic(file_path):
    """
    使用基本CSV处理读取训练日志，作为pandas的备用方案
    
    参数:
    - file_path: CSV文件路径
    
    返回:
    - 字典，键为列名，值为列数据列表
    """
    data = {}
    
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        headers = next(reader)  # 读取表头
        
        # 初始化数据字典
        for header in headers:
            data[header] = []
        
        # 读取数据
        for row in reader:
            for i, value in enumerate(row):
                try:
                    # 尝试转换为浮点数
                    data[headers[i]].append(float(value))
                except ValueError:
                    # 如果转换失败，保留为字符串
                    data[headers[i]].append(value)
    
    # 计算移动平均（如果有reward列）
    if 'reward' in data:
        data['reward_moving_avg'] = moving_average(data['reward'], 50)
    
    return data


def moving_average(data, window_size):
    """计算移动平均"""
    result = []
    for i in range(len(data)):
        start_idx = max(0, i - window_size + 1)
        result.append(sum(data[start_idx:i+1]) / (i - start_idx + 1))
    return result


if __name__ == "__main__":
    # 测试绘图函数
    import numpy as np
    
    # 生成模拟训练数据
    steps = np.arange(1000)
    rewards = np.random.normal(0, 1, 1000).cumsum() / 100
    losses = np.exp(-steps / 500) + np.random.normal(0, 0.1, 1000)
    comm_qualities = np.random.normal(0.5, 0.1, 1000)
    interferences = np.random.normal(0.7, 0.1, 1000)
    
    # 创建模拟日志文件
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), PLOT_DIR)
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'mock_training_log.csv')
    
    # 保存为CSV文件
    with open(log_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['step', 'reward', 'loss', 'mean_comm_quality', 'total_interference'])
        for i in range(1000):
            writer.writerow([steps[i], rewards[i], losses[i], comm_qualities[i], interferences[i]])
    
    # 测试绘制训练曲线
    plot_training_curves(log_file, os.path.join(log_dir, 'training_curves.png'), show=False)
    
    # 测试绘制评估结果
    eval_results = {
        'rewards': np.random.normal(0.5, 0.2, 100),
        'comm_qualities': np.random.normal(0.6, 0.1, 100),
        'interferences': np.random.normal(0.7, 0.15, 100)
    }
    plot_evaluation_results(eval_results, os.path.join(log_dir, 'evaluation_results.png'), show=False)
    
    # 测试绘制聚类分析
    buoy_positions = np.random.uniform(0, AREA_SIZE, (200, 2))
    
    if SKLEARN_AVAILABLE:
        kmeans = KMeans(n_clusters=6, random_state=0).fit(buoy_positions)
        cluster_labels = kmeans.labels_
        cluster_centers = kmeans.cluster_centers_
    else:
        # 简单聚类替代方案
        cluster_labels = np.zeros(200, dtype=int)
        for i in range(200):
            # 根据位置简单分为6类
            x, y = buoy_positions[i]
            if x < AREA_SIZE/3:
                if y < AREA_SIZE/3:
                    cluster_labels[i] = 0
                elif y < 2*AREA_SIZE/3:
                    cluster_labels[i] = 1
                else:
                    cluster_labels[i] = 2
            else:
                if y < AREA_SIZE/3:
                    cluster_labels[i] = 3
                elif y < 2*AREA_SIZE/3:
                    cluster_labels[i] = 4
                else:
                    cluster_labels[i] = 5
        
        # 计算聚类中心
        cluster_centers = np.zeros((6, 2))
        for i in range(6):
            mask = cluster_labels == i
            if np.any(mask):
                cluster_centers[i] = np.mean(buoy_positions[mask], axis=0)
    
    visualize_cluster_analysis(
        buoy_positions, 
        cluster_labels, 
        cluster_centers,
        os.path.join(log_dir, 'cluster_analysis.png'),
        show=False
    ) 
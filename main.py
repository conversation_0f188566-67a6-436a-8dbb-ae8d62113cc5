import os
import sys
import argparse
import time
import numpy as np

from environment import BuoyEnvironment
from rl_training import PPOAgent, HierarchicalAgent
from visualization.plot_utils import plot_training_curves, plot_evaluation_results
from config import *


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="浮标-无人机交互强化学习")
    
    # 主要命令
    parser.add_argument("command", choices=["train", "evaluate", "visualize", "test"],
                        help="要执行的命令")
    
    # 训练参数
    parser.add_argument("--approach", choices=["movement", "interference", "combined", "hierarchical"],
                        default=TRAINING_APPROACH,
                        help="训练方法 (默认: %(default)s)")
    parser.add_argument("--timesteps", type=int, default=TOTAL_TIMESTEPS,
                        help="训练总步数 (默认: %(default)s)")
    parser.add_argument("--seed", type=int, default=None,
                        help="随机种子 (默认: 当前时间戳)")
    
    # 评估参数
    parser.add_argument("--model", type=str, default=None,
                        help="要评估的模型路径")
    parser.add_argument("--episodes", type=int, default=30,
                        help="评估回合数 (默认: %(default)s)")
    parser.add_argument("--render", action="store_true",
                        help="是否渲染环境")
    
    # 可视化参数
    parser.add_argument("--log", type=str, default=None,
                        help="训练日志文件路径")
    parser.add_argument("--output", type=str, default=None,
                        help="输出图像文件路径")
    
    return parser.parse_args()


def train(args):
    """训练模型"""
    print(f"开始训练 (方法: {args.approach}, 步数: {args.timesteps}, 种子: {args.seed})")
    
    if args.approach == "hierarchical":
        # 使用层次化代理
        agent = HierarchicalAgent(seed=args.seed)
        agent.train(total_timesteps=args.timesteps)
    else:
        # 使用PPO代理
        agent = PPOAgent(training_approach=args.approach, seed=args.seed)
        agent.train(total_timesteps=args.timesteps)
    
    print("训练完成")


def evaluate(args):
    """评估模型"""
    if args.model is None:
        print("错误: 请指定要评估的模型路径")
        return
    
    print(f"开始评估 (模型: {args.model}, 回合数: {args.episodes}, 渲染: {args.render})")
    
    if "movement" in args.model and "interference" in args.model:
        # 层次化模型评估
        agent = HierarchicalAgent(seed=args.seed)
        movement_model_path = args.model.split(",")[0]
        interference_model_path = args.model.split(",")[1]
        agent.load(movement_model_path, interference_model_path)
    else:
        # 单一模型评估
        agent = PPOAgent(seed=args.seed)
        agent.load(args.model)
    
    # 评估模型
    eval_results = agent.evaluate(n_episodes=args.episodes, render=args.render)
    
    # 打印评估结果
    print("\n评估结果:")
    print(f"平均奖励: {np.mean(eval_results['rewards']):.4f} ± {np.std(eval_results['rewards']):.4f}")
    print(f"平均通信质量: {np.mean(eval_results['comm_qualities']):.4f} ± {np.std(eval_results['comm_qualities']):.4f}")
    print(f"平均干扰效果: {np.mean(eval_results['interferences']):.4f} ± {np.std(eval_results['interferences']):.4f}")
    
    # 绘制评估结果
    output_path = args.output or os.path.join(PLOT_DIR, "evaluation_results.png")
    plot_evaluation_results(eval_results, save_path=output_path)


def visualize(args):
    """可视化训练曲线"""
    if args.log is None:
        print("错误: 请指定训练日志文件路径")
        return
    
    print(f"可视化训练曲线 (日志: {args.log})")
    
    output_path = args.output or os.path.join(PLOT_DIR, "training_curves.png")
    plot_training_curves(args.log, save_path=output_path)


def test(args):
    """测试环境"""
    print("测试环境...")
    
    env = BuoyEnvironment(render_mode='human')
    obs, _ = env.reset()
    
    print(f"观测空间形状: {obs.shape}")
    print(f"动作空间: {env.action_space}")
    
    # 执行随机动作
    for step in range(100):
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        env.render()
        
        print(f"步骤 {step+1}/100, 奖励: {reward:.4f}, "
              f"平均通信质量: {info['mean_comm_quality']:.4f}, "
              f"总干扰效果: {info['total_interference']:.4f}")
        
        time.sleep(0.1)  # 暂停以便观察
        
        if terminated or truncated:
            obs, _ = env.reset()
    
    env.close()


def main():
    """主函数"""
    args = parse_args()
    
    # 创建必要的目录
    os.makedirs(LOG_DIR, exist_ok=True)
    os.makedirs(MODEL_DIR, exist_ok=True)
    os.makedirs(TENSORBOARD_DIR, exist_ok=True)
    os.makedirs(PLOT_DIR, exist_ok=True)
    
    # 执行相应的命令
    if args.command == "train":
        train(args)
    elif args.command == "evaluate":
        evaluate(args)
    elif args.command == "visualize":
        visualize(args)
    elif args.command == "test":
        test(args)


if __name__ == "__main__":
    main() 
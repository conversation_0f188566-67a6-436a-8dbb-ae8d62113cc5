1. 系统概述
本系统设计一个海上通信网络模拟环境，包含200个浮标和6架无人机。浮标之间形成动态通信网络，无人机通过选择不同干扰模式来影响浮标间的通信。系统目标是使用强化学习训练无人机，使其能够智能选择移动路径和干扰模式，以最优方式达成预设目标。

2. 系统组成
2.1 浮标子系统
数量：200个

分布：随机均匀分布在10km×10km的海域内
建立坐标系  1m为单位

通信特性：

每200秒随机切换一次通信对象和通信方式

最大通信距离：3km

20种通信方式(通信1-通信20)

状态属性：

置坐标(x,y)

当前通信对象ID

当前通信方式ID
位

通信质量：只有通和不通  被干扰就不通 否则就通

2.2 无人机子系统
数量：6架

移动特性：

固定速度：60km/h(16.67m/s)

动作空间：{前进，后退，左移，右移，原地不动}

移动步长：每决策周期(10秒)移动约166.7米

高度固定：不考虑高度 现在是2D的

干扰特性：

10种干扰模式(干扰1-干扰10)

干扰范围：半径1km的圆形区域

干扰决策周期：与移动决策同步，每10秒一次

状态属性：

位置坐标(x,y)

当前干扰模式

4. 系统动态机制
4.1 时间同步
浮标通信更新周期：200s

无人机决策周期：10秒

仿真时钟精度：1秒















干扰模式\通信方式	通信1	通信2	通信3	通信4	通信5	通信6	通信7	通信8	通信9	通信10	通信11	通信12	通信13	通信14	通信15	通信16	通信17	通信18	通信19	通信20
干扰1	0.85	0.10	0.30	0.45	0.60	0.25	0.70	0.15	0.50	0.35	0.20	0.65	0.40	0.55	0.75	0.05	0.80	0.90	0.25	0.50
干扰2	0.20	0.75	0.85	0.30	0.45	0.60	0.15	0.70	0.25	0.50	0.35	0.10	0.65	0.40	0.55	0.80	0.05	0.30	0.90	0.45
干扰3	0.50	0.35	0.20	0.95	0.10	0.30	0.45	0.60	0.25	0.70	0.15	0.50	0.35	0.80	0.05	0.40	0.55	0.75	0.65	0.90
干扰4	0.25	0.70	0.15	0.50	0.85	0.10	0.30	0.45	0.60	0.25	0.70	0.15	0.50	0.35	0.20	0.65	0.40	0.55	0.75	0.05
干扰5	0.60	0.25	0.70	0.15	0.50	0.95	0.10	0.30	0.45	0.60	0.25	0.70	0.15	0.50	0.35	0.20	0.65	0.40	0.55	0.75
干扰6	0.35	0.20	0.65	0.40	0.55	0.75	0.05	0.80	0.90	0.25	0.50	0.35	0.20	0.65	0.40	0.55	0.75	0.05	0.80	0.90
干扰7	0.70	0.15	0.50	0.35	0.20	0.65	0.80	0.05	0.30	0.90	0.45	0.60	0.25	0.70	0.15	0.50	0.35	0.20	0.65	0.40
干扰8	0.10	0.30	0.45	0.60	0.25	0.70	0.15	0.50	0.35	0.20	0.65	0.40	0.55	0.75	0.05	0.80	0.90	0.25	0.50	0.35
干扰9	0.45	0.60	0.25	0.70	0.15	0.50	0.35	0.20	0.65	0.40	0.55	0.75	0.05	0.80	0.90	0.25	0.50	0.35	0.20	0.65
干扰10	0.15	0.50	0.35	0.20	0.65	0.40	0.55	0.75	0.05	0.80	0.90	0.25	0.50	0.35	0.20	0.65	0.40	0.55	0.75	0.05

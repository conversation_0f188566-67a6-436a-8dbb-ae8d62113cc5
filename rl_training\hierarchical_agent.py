import os
import sys
import time
import numpy as np
import pandas as pd
from stable_baselines3 import PPO
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.vec_env import SubprocVecEnv
from stable_baselines3.common.utils import set_random_seed
from stable_baselines3.common.monitor import Monitor

# 添加项目根目录到路径，以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import *
from environment import BuoyEnvironment
from visualization.plot_utils import visualize_cluster_analysis

# 尝试导入scikit-learn，如果不可用则提供备用方案
try:
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: scikit-learn库不可用，将使用简单聚类替代方案")


class ClusterAnalyzer:
    """
    浮标聚类分析器，用于找到最佳干扰点  -- 考虑的是所有浮标的位置
    """
    def __init__(self, n_clusters=NUM_UAVS):
        self.n_clusters = n_clusters
        self.kmeans = None
        self.cluster_labels = None
        self.cluster_centers = None
    
    def analyze(self, buoy_positions):
        """
        分析浮标位置，找到最佳干扰点
        
        参数:
        - buoy_positions: 浮标位置数组，形状为 (num_buoys, 2)
        
        返回:
        - cluster_centers: 聚类中心数组，形状为 (n_clusters, 2)
        - labels: 聚类标签数组，形状为 (num_buoys,)
        """
        if SKLEARN_AVAILABLE:
            # 使用KMeans聚类
            self.kmeans = KMeans(n_clusters=self.n_clusters, random_state=0)
            self.kmeans.fit(buoy_positions)
            self.cluster_labels = self.kmeans.labels_
            self.cluster_centers = self.kmeans.cluster_centers_
        else:
            # 简单聚类替代方案
            self.cluster_labels = self._simple_clustering(buoy_positions)
            self.cluster_centers = self._compute_cluster_centers(buoy_positions, self.cluster_labels)
        
        return self.cluster_centers, self.cluster_labels
    
    def _simple_clustering(self, buoy_positions):
        """简单聚类替代方案"""
        num_buoys = buoy_positions.shape[0]
        labels = np.zeros(num_buoys, dtype=int)
        
        # 根据位置将浮标分为n_clusters类
        x_divisions = int(np.sqrt(self.n_clusters))
        y_divisions = self.n_clusters // x_divisions
        
        x_step = AREA_SIZE / x_divisions
        y_step = AREA_SIZE / y_divisions
        
        for i in range(num_buoys):
            x, y = buoy_positions[i]
            x_idx = min(int(x / x_step), x_divisions - 1)
            y_idx = min(int(y / y_step), y_divisions - 1)
            labels[i] = x_idx + y_idx * x_divisions
        
        return labels
    
    def _compute_cluster_centers(self, buoy_positions, labels):
        """计算聚类中心"""
        centers = np.zeros((self.n_clusters, 2))
        for i in range(self.n_clusters):
            mask = labels == i
            if np.any(mask):
                centers[i] = np.mean(buoy_positions[mask], axis=0)
            else:
                # 如果没有浮标属于该类，则随机生成一个中心
                centers[i] = np.random.uniform(0, AREA_SIZE, 2)
        return centers
    
    def visualize(self, buoy_positions, save_path=None, show=True):
        """可视化聚类结果"""
        if self.cluster_labels is None or self.cluster_centers is None:
            raise ValueError("请先调用analyze方法进行聚类分析")
        
        visualize_cluster_analysis(
            buoy_positions,
            self.cluster_labels,
            self.cluster_centers,
            save_path=save_path,
            show=show
        )


class HierarchicalAgent:
    """
    层次化强化学习代理
    
    1. 使用聚类分析找到最佳干扰点
    2. 训练无人机移动到干扰点
    3. 训练无人机选择最佳干扰模式
    """
    def __init__(self, seed=None):
        self.seed = seed if seed is not None else int(time.time())
        
        # 设置随机种子
        set_random_seed(self.seed)
        
        # 创建目录
        self.log_dir = LOG_DIR
        self.model_dir = MODEL_DIR
        self.tensorboard_dir = TENSORBOARD_DIR
        self.plot_dir = PLOT_DIR
        
        os.makedirs(self.log_dir, exist_ok=True)
        os.makedirs(self.model_dir, exist_ok=True)
        os.makedirs(self.tensorboard_dir, exist_ok=True)
        os.makedirs(self.plot_dir, exist_ok=True)
        
        # 初始化组件
        self.cluster_analyzer = ClusterAnalyzer(n_clusters=NUM_UAVS)
        self.movement_model = None
        self.interference_model = None
        
        # 聚类结果
        self.cluster_centers = None
    
    def train(self, total_timesteps=TOTAL_TIMESTEPS):
        """训练层次化代理"""
        # 第1步：创建环境并获取浮标位置
        env = BuoyEnvironment()
        obs, _ = env.reset()
        buoy_positions = env.buoy_positions
        
        # 第2步：聚类分析找到最佳干扰点
        print("正在进行聚类分析...")
        self.cluster_centers, labels = self.cluster_analyzer.analyze(buoy_positions)
        
        # 可视化聚类结果
        self.cluster_analyzer.visualize(
            buoy_positions,
            save_path=os.path.join(self.plot_dir, 'cluster_analysis.png'),
            show=False
        )
        
        print(f"聚类分析完成，找到{len(self.cluster_centers)}个干扰点")
        
        # 第3步：训练无人机移动
        print("正在训练无人机移动策略...")
        self._train_movement(total_timesteps // 2)
        
        # 第4步：训练无人机干扰模式选择
        print("正在训练无人机干扰模式选择策略...")
        self._train_interference(total_timesteps // 2)
        
        return self.movement_model, self.interference_model
    
    def _train_movement(self, total_timesteps):
        """训练无人机移动策略"""
        # 创建环境
        env = self._create_env("movement")
        
        # 创建PPO模型
        self.movement_model = PPO(
            "MlpPolicy",
            env,
            verbose=1,
            learning_rate=LEARNING_RATE,
            n_steps=N_STEPS,
            batch_size=BATCH_SIZE,
            n_epochs=N_EPOCHS,
            gamma=GAMMA,
            gae_lambda=GAE_LAMBDA,
            clip_range=CLIP_RANGE,
            ent_coef=ENT_COEF,
            tensorboard_log=os.path.join(self.tensorboard_dir, "movement")
        )
        
        # 训练模型
        self.movement_model.learn(
            total_timesteps=total_timesteps,
            progress_bar=True
        )
        
        # 保存模型
        model_path = os.path.join(self.model_dir, "ppo_buoy_movement")
        self.movement_model.save(model_path)
        print(f"移动策略模型已保存到: {model_path}")
    
    def _train_interference(self, total_timesteps):
        """训练无人机干扰模式选择策略"""
        # 创建环境
        env = self._create_env("interference")
        
        # 创建PPO模型
        self.interference_model = PPO(
            "MlpPolicy",
            env,
            verbose=1,
            learning_rate=LEARNING_RATE,
            n_steps=N_STEPS,
            batch_size=BATCH_SIZE,
            n_epochs=N_EPOCHS,
            gamma=GAMMA,
            gae_lambda=GAE_LAMBDA,
            clip_range=CLIP_RANGE,
            ent_coef=ENT_COEF,
            tensorboard_log=os.path.join(self.tensorboard_dir, "interference")
        )
        
        # 训练模型
        self.interference_model.learn(
            total_timesteps=total_timesteps,
            progress_bar=True
        )
        
        # 保存模型
        model_path = os.path.join(self.model_dir, "ppo_buoy_interference")
        self.interference_model.save(model_path)
        print(f"干扰模式选择策略模型已保存到: {model_path}")
    
    def _create_env(self, training_approach, n_envs=NUM_ENVS):
        """创建向量化环境"""
        def make_env(rank):
            def _init():
                env = BuoyEnvironment(training_approach=training_approach)
                env = Monitor(env)
                env.seed(self.seed + rank)
                return env
            return _init
        
        return SubprocVecEnv([make_env(i) for i in range(n_envs)])
    
    def load(self, movement_model_path, interference_model_path):
        """加载已训练的模型"""
        self.movement_model = PPO.load(movement_model_path)
        self.interference_model = PPO.load(interference_model_path)
        return self.movement_model, self.interference_model
    
    def evaluate(self, n_episodes=10, render=False):
        """评估模型"""
        if self.movement_model is None or self.interference_model is None:
            raise ValueError("模型未初始化，请先训练或加载模型")
        
        # 创建评估环境
        env = BuoyEnvironment(render_mode='human' if render else None)
        
        # 评估结果
        rewards = []
        comm_qualities = []
        interferences = []
        
        for episode in range(n_episodes):
            obs, _ = env.reset()
            done = False
            truncated = False
            episode_reward = 0
            episode_comm_qualities = []
            episode_interferences = []
            
            # 首先使用移动模型将无人机移动到最佳位置
            for _ in range(20):  # 给予足够的步骤让无人机移动
                action, _ = self.movement_model.predict(obs, deterministic=True)
                obs, reward, done, truncated, info = env.step(action)
                
                if render:
                    env.render()
                
                episode_reward += reward
                episode_comm_qualities.append(info['mean_comm_quality'])
                episode_interferences.append(info['total_interference'])
                
                if done or truncated:
                    break
            
            # 然后使用干扰模式选择模型选择最佳干扰模式
            while not (done or truncated):
                action, _ = self.interference_model.predict(obs, deterministic=True)
                obs, reward, done, truncated, info = env.step(action)
                
                if render:
                    env.render()
                
                episode_reward += reward
                episode_comm_qualities.append(info['mean_comm_quality'])
                episode_interferences.append(info['total_interference'])
            
            rewards.append(episode_reward)
            comm_qualities.append(np.mean(episode_comm_qualities))
            interferences.append(np.mean(episode_interferences))
            
            print(f"Episode {episode+1}/{n_episodes}, Reward: {episode_reward:.4f}, "
                  f"Mean Comm Quality: {np.mean(episode_comm_qualities):.4f}, "
                  f"Mean Interference: {np.mean(episode_interferences):.4f}")
        
        # 关闭环境
        env.close()
        
        # 返回评估结果
        return {
            'rewards': rewards,
            'comm_qualities': comm_qualities,
            'interferences': interferences
        }


if __name__ == "__main__":
    # 创建层次化代理
    agent = HierarchicalAgent()
    
    # 训练代理
    movement_model, interference_model = agent.train(total_timesteps=10000)  # 小规模训练用于测试
    
    # 评估代理
    eval_results = agent.evaluate(n_episodes=5, render=True)
    
    # 绘制评估结果
    from visualization.plot_utils import plot_evaluation_results
    
    plot_evaluation_results(
        eval_results,
        save_path=os.path.join(PLOT_DIR, 'hierarchical_evaluation_results.png')
    ) 